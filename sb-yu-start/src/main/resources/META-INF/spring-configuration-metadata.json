{"groups": [{"name": "com.efficient.ykz", "type": "com.efficient.ykz.properties.YkzProperties", "sourceType": "com.efficient.ykz.config.YkzConfig"}], "properties": [{"name": "com.efficient.ykz.userCenter.handle", "type": "java.lang.String", "description": "用户中心数据处理逻辑，default，custom", "defaultValue": "default", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.handleClassName", "type": "java.lang.String", "description": "handle 为 custom时必填，全限定名称", "defaultValue": "", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.ip", "type": "java.lang.String", "description": "YKZ IP", "defaultValue": "https://uc-openplatform.bigdatacq.com:4403", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.appId", "type": "java.lang.String", "description": "YKZ  appId", "defaultValue": "", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.appSecret", "type": "java.lang.String", "description": "YKZ  appSecret", "defaultValue": "", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.db", "type": "java.lang.Bo<PERSON>an", "description": "拉取的数据是否需要入库", "defaultValue": false, "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.accessTokenUrl", "type": "java.lang.String", "description": "获取access_token", "defaultValue": "/ykz/access_token", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.orgByCode", "type": "java.lang.String", "description": "根据组织机构code查询详细信息", "defaultValue": "/ykz/org/getByOrganizationCode", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.orgByCodeList", "type": "java.lang.String", "description": "根据组织机构code批量查询详细信息", "defaultValue": "/ykz/org/listByOrganizationCodes", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.orgByParentCode", "type": "java.lang.String", "description": "根据父级组织机构分页查询子级信息", "defaultValue": "/ykz/org/pageByParentOrganizationCode", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.userByMobile", "type": "java.lang.String", "description": "根据父级组织机构分页查询子级信息", "defaultValue": "/ykz/org/pageByParentOrganizationCode", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.userByMobileList", "type": "java.lang.String", "description": "批量根据用户手机号查询用户信息", "defaultValue": "/ykz/user/listByMobiles", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.userByZwddId", "type": "java.lang.String", "description": "根据用户政钉ID查询用户信息", "defaultValue": "/ykz/user/getByAccountId", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.userByZwddIdList", "type": "java.lang.String", "description": "批量根据用户政钉ID查询用户信息", "defaultValue": "/ykz/user/listByAccountIds", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.userPostByZwddId", "type": "java.lang.String", "description": "根据用户ID查询用户职位信息", "defaultValue": "/ykz/user/listUserPostByAccountId", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.userTagByZwddId", "type": "java.lang.String", "description": "根据用户政钉Id查询用户标签信息", "defaultValue": "/ykz/user/getLabelByAccountId", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.userTagByMobile", "type": "java.lang.String", "description": "根据用户手机号查询用户标签信息", "defaultValue": "/ykz/user/getLabelByMobile", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.userTagByZwddIdList", "type": "java.lang.String", "description": "批量根据用户政钉Id查询用户标签信息", "defaultValue": "/ykz/user/listLabelByAccountIds", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter.userTagByMobileList", "type": "java.lang.String", "description": "批量根据用户手机号查询用户标签信息", "defaultValue": "/ykz/user/listLabelByMobiles", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter..enable-salt", "type": "java.lang.Bo<PERSON>an", "description": "密码是否加盐", "defaultValue": true, "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.userCenter..salt-value", "type": "java.lang.String", "description": "密码盐值", "defaultValue": "1809", "sourceType": "com.efficient.ykz.properties.YkzUserCenter"}, {"name": "com.efficient.ykz.ykzApi.domainName", "type": "java.lang.String", "description": "用户中心 IP", "defaultValue": "zd-openplatform.bigdatacq.com", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.protocal", "type": "java.lang.String", "description": "协议", "defaultValue": "https", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.appkey", "type": "java.lang.String", "description": "appkey", "defaultValue": "", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.appsecret", "type": "java.lang.String", "description": "appsecret", "defaultValue": "", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.winOpen", "type": "java.lang.Bo<PERSON>an", "description": "跳转方式，默认外部浏览器打开，目前只针对pc端", "defaultValue": true, "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.pcUrl", "type": "java.lang.String", "description": "pc访问地址前缀", "defaultValue": true, "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.appUrl", "type": "java.lang.String", "description": "app访问地址前缀", "defaultValue": true, "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.accessToken", "type": "java.lang.String", "description": "获取 accessToken", "defaultValue": "/gettoken.json", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.userInfo", "type": "java.lang.String", "description": "获取用户信息", "defaultValue": "/rpc/oauth2/dingtalk_app_user.json", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.tokenInfo", "type": "java.lang.String", "description": "获取token信息", "defaultValue": "/rpc/oauth2/dingtalk_app_token.json", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.sendMsg", "type": "java.lang.String", "description": "发送消息", "defaultValue": "/chat/sendMsg", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.sendWorkNotice", "type": "java.lang.String", "description": "发送工作通知", "defaultValue": "/message/workNotification", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.revokeWorkNotice", "type": "java.lang.String", "description": "撤销工作通知", "defaultValue": "/message/revoke", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.createTodo", "type": "java.lang.String", "description": "创建待办", "defaultValue": "/tc/v2/openapi/task/create.json", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.finishTodo", "type": "java.lang.String", "description": "完成待办", "defaultValue": "/tc/openapi/task/finish.json", "sourceType": "com.efficient.ykz.properties.YkzApi"}, {"name": "com.efficient.ykz.ykzApi.cancelTodo", "type": "java.lang.String", "description": "撤销待办", "defaultValue": "/tc/openapi/task/cancel.json"}], "hints": [{"name": "com.efficient.ykz.userCenter.handle", "values": [{"value": "default", "description": "默认"}, {"value": "custom", "description": "自定义"}]}]}