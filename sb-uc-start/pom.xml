<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.zenith</groupId>
        <artifactId>sb</artifactId>
        <version>2.2.1-20250728-02-beta</version>
    </parent>
    <description>用户体系 start</description>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>sb-uc-start</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-common</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-logs-start</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-swagger-start</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-cache-start</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-file-start</artifactId>
            <scope>provided</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.zenith</groupId>-->
<!--            <artifactId>sb-auth-start</artifactId>-->
<!--            <scope>provided</scope>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
