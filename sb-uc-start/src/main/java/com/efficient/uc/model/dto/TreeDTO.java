package com.efficient.uc.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * TreeDTO DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-16 16:47:29
 */
@Data
@ApiModel("机构树 请求实体-TreeDTO")
public class TreeDTO implements Serializable {
    private static final long serialVersionUID = 210030674791845706L;

    /**
     * 层级码
     */
    @ApiModelProperty(value = "层级码")
    private String code;

}

