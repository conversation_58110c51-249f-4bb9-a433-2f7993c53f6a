package com.efficient.uc.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户职位信息 Excel
 * </p>
 *
 * <AUTHOR>
 * @date 2024-11-15 17:25:40
 */
@Data
@ColumnWidth(value = 15)
@HeadRowHeight(value = 20)
@ContentRowHeight(value = 15)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ApiModel("用户职位信息 请求实体-SysUserPostExcel")
public class SysUserPostExcel implements Serializable {
    private static final long serialVersionUID = 7757889930837015025L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    @ApiModelProperty(value = "部门ID")
    private String deptId;
    /**
     * 部门层级码
     */
    @ExcelProperty(value = "部门层级码")
    @ApiModelProperty(value = "部门层级码")
    private String deptLevelCode;
    /**
     * 单位ID
     */
    @ExcelProperty(value = "单位ID")
    @ApiModelProperty(value = "单位ID")
    private String unitId;
    /**
     * 单位层级码
     */
    @ExcelProperty(value = "单位层级码")
    @ApiModelProperty(value = "单位层级码")
    private String unitLevelCode;
    /**
     * 权限类型，1-个人，2-部门，3-单位，9-自定义
     */
    @ExcelProperty(value = "权限类型，1-个人，2-部门，3-单位，9-自定义")
    @ApiModelProperty(value = "权限类型，1-个人，2-部门，3-单位，9-自定义")
    private String permissionType;
    /**
     * 是否主职务
     */
    @ExcelProperty(value = "是否主职务")
    @ApiModelProperty(value = "是否主职务")
    private Integer mainJob;
    /**
     * 加入时间
     */
    @ExcelProperty(value = "加入时间")
    @ApiModelProperty(value = "加入时间")
    private Date joinDate;
    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    @ApiModelProperty(value = "排序")
    private Long sort;
    /**
     * 职务
     */
    @ExcelProperty(value = "职务")
    @ApiModelProperty(value = "职务")
    private String postName;
    /**
     * create_time
     */
    @ExcelProperty(value = "create_time")
    @ApiModelProperty(value = "create_time")
    private Date createTime;
    /**
     * update_time
     */
    @ExcelProperty(value = "update_time")
    @ApiModelProperty(value = "update_time")
    private Date updateTime;
    /**
     * is_delete
     */
    @ExcelProperty(value = "is_delete")
    @ApiModelProperty(value = "is_delete")
    private Integer isDelete;
    /**
     * 拉取时间
     */
    @ExcelProperty(value = "拉取时间")
    @ApiModelProperty(value = "拉取时间")
    private Date pullTime;

}

