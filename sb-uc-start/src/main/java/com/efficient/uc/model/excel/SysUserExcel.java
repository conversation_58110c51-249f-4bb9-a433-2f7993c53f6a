package com.efficient.uc.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用户信息 Excel
 * </p>
 *
 * <AUTHOR>
 * @date 2024-11-15 17:25:40
 */
@Data
@ColumnWidth(value = 15)
@HeadRowHeight(value = 20)
@ContentRowHeight(value = 15)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ApiModel("用户信息 请求实体-SysUserExcel")
public class SysUserExcel implements Serializable {
    private static final long serialVersionUID = 23403257097074335L;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 账号
     */
    @ExcelProperty(value = "账号")
    @ApiModelProperty(value = "账号")
    private String account;
    /**
     * 密码
     */
    @ExcelProperty(value = "密码")
    @ApiModelProperty(value = "密码")
    private String password;
    /**
     * 政务钉Id
     */
    @ExcelProperty(value = "政务钉Id")
    @ApiModelProperty(value = "政务钉Id")
    private String zwddId;
    /**
     * 电话
     */
    @ExcelProperty(value = "电话")
    @ApiModelProperty(value = "电话")
    private String phone;
    /**
     * 身份证
     */
    @ExcelProperty(value = "身份证")
    @ApiModelProperty(value = "身份证")
    private String idCard;

}

