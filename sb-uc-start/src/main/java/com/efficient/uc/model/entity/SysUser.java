package com.efficient.uc.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户信息 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@Data
@TableName("efficient_sys_user")
@ApiModel("用户信息")
public class SysUser implements Serializable {

    private static final long serialVersionUID = 2813807927828104438L;

    /**
     * 用户中心 ID
     */
    @ApiModelProperty(value = "用户中心 ID")
    @TableId(value = "id")
    private String id;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @TableField("name")
    private String name;
    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    @TableField("account")
    private String account;
    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    @TableField("password")
    private String password;
    /**
     * 政务钉Id
     */
    @ApiModelProperty(value = "政务钉Id")
    @TableField("zwdd_id")
    private String zwddId;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @TableField("phone")
    private String phone;
    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    @TableField("id_card")
    private String idCard;
    /**
     * 是否启用 1-启用，0-停用
     */
    @ApiModelProperty(value = "是否启用 1-启用，0-停用")
    @TableField("is_enable")
    private Integer isEnable;
    /**
     * 过期时间
     */
    @ApiModelProperty(value = "过期时间")
    @TableField(value = "expiration_time", updateStrategy = FieldStrategy.ALWAYS)
    private Date expirationTime;
    /**
     * 上次修改密码时间
     */
    @ApiModelProperty(value = "上次修改密码时间")
    @TableField("update_password_time")
    private Date updatePasswordTime;
    /**
     * 上传登录时间
     */
    @ApiModelProperty(value = "上传登录时间")
    @TableField("last_login_time")
    private Date lastLoginTime;
    /**
     * 创建时间戳
     */
    @ApiModelProperty(value = "创建时间戳")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     *
     */
    @ApiModelProperty(value = "")
    @TableField("is_delete")
    @TableLogic
    private Integer isDelete;
    /**
     * 拉取时间
     */
    @ApiModelProperty(value = "拉取时间")
    @TableField("pull_time")
    private Date pullTime;
}
