package com.efficient.uc.model.converter;

import com.efficient.uc.model.dto.SysUnitDTO;
import com.efficient.uc.model.entity.SysUnit;
import com.efficient.uc.model.excel.SysUnitExcel;
import com.efficient.uc.model.vo.SysUnitVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 机构数据 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@Mapper(componentModel = "spring")
public interface SysUnitConverter {

    SysUnitConverter INSTANCE = Mappers.getMapper(SysUnitConverter.class);

    @Mappings({})
    SysUnit dto2Entity(SysUnitDTO dto);

    @Mappings({})
    SysUnitVO entity2Vo(SysUnit entity);

    @Mappings({})
    SysUnit excel2Entity(SysUnitExcel excel);

    @Mappings({})
    SysUnitExcel entity2Excel(SysUnit entity);
}
