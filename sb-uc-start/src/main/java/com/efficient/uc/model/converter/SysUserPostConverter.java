package com.efficient.uc.model.converter;

import com.efficient.uc.model.dto.SysUserPostDTO;
import com.efficient.uc.model.entity.SysUserPost;
import com.efficient.uc.model.excel.SysUserPostExcel;
import com.efficient.uc.model.vo.SysUserPostVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 用户职位信息 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@Mapper(componentModel = "spring")
public interface SysUserPostConverter {

    SysUserPostConverter INSTANCE = Mappers.getMapper(SysUserPostConverter.class);

    @Mappings({})
    SysUserPost dto2Entity(SysUserPostDTO dto);

    @Mappings({})
    SysUserPostVO entity2Vo(SysUserPost entity);

    @Mappings({})
    SysUserPost excel2Entity(SysUserPostExcel excel);

    @Mappings({})
    SysUserPostExcel entity2Excel(SysUserPost entity);
}
