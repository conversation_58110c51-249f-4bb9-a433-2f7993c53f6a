package com.efficient.uc.model.converter;

import com.efficient.uc.model.dto.SysUserDTO;
import com.efficient.uc.model.entity.SysUser;
import com.efficient.uc.model.excel.SysUserExcel;
import com.efficient.uc.model.vo.SysUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <p>
 * 用户信息 模型转换器
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@Mapper(componentModel = "spring")
public interface SysUserConverter {

    SysUserConverter INSTANCE = Mappers.getMapper(SysUserConverter.class);

    @Mappings({})
    SysUser dto2Entity(SysUserDTO dto);

    @Mappings({})
    SysUserVO entity2Vo(SysUser entity);

    @Mappings({})
    SysUser excel2Entity(SysUserExcel excel);

    @Mappings({})
    SysUserExcel entity2Excel(SysUser entity);
}
