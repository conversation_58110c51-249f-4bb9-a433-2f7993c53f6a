package com.efficient.uc.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.entity.TreeNode;
import com.efficient.common.result.Result;
import com.efficient.uc.model.dto.SysUnitDTO;
import com.efficient.uc.model.dto.SysUnitListDTO;
import com.efficient.uc.model.dto.TreeDTO;
import com.efficient.uc.model.entity.SysUnit;
import com.efficient.uc.model.vo.SysUnitVO;

import java.util.List;

/**
 * <p>
 * 机构数据 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
public interface SysUnitService extends IService<SysUnit> {

    /***
     * 新增
     */
    Result<SysUnit> save(SysUnitDTO dto);

    /**
     * 详情
     */
    Result<SysUnitVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(SysUnitDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<SysUnitVO> list(SysUnitListDTO dto);

    String createLevelCode(String parentId);

    String createLevelCodeByCode(String orgCode);

    String getBelongById(String id);

    SysUnit getByOrgCode(String organizationCode);

    SysUnit getUnitByDeptId(String deptId);

    SysUnit getUnitByOrgCode(String orgCode);

    String getNextLevelCode(String levelCode);

    Result<TreeNode> tree(TreeDTO dto);

    List<TreeNode> buildTreeNode(String rootCode, List<SysUnit> sysUnitList, boolean checkIsLeaf);

    Result<TreeNode> treeAll(TreeDTO dto);
}
