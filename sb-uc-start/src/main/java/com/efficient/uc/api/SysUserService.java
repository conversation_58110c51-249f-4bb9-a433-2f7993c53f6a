package com.efficient.uc.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.efficient.file.model.vo.FileVO;
import com.efficient.uc.model.dto.SysUserDTO;
import com.efficient.uc.model.dto.SysUserListDTO;
import com.efficient.uc.model.entity.SysUser;
import com.efficient.uc.model.vo.SysUserVO;

import java.util.Date;

/**
 * <p>
 * 用户信息 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
public interface SysUserService extends IService<SysUser> {

    /***
     * 新增
     */
    Result<SysUser> save(SysUserDTO dto);

    /**
     * 详情
     */
    Result<SysUserVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(SysUserDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<SysUserVO> list(SysUserListDTO dto);

    /**
     * 导入
     */
    Result<Boolean> importExcel(String fileId);

    /**
     * 导出
     */
    Result<FileVO> exportExcel(SysUserListDTO dto);

    SysUser getByZwddId(String zwddId);

    SysUser getByAccount(String account);

    boolean unLockUser(String userId);

    boolean lockUser(String userId, Date unLockTime);

    Result<Boolean> resetPassword(String userId, String oldPassword, String newPassword, Boolean isReset);
}
