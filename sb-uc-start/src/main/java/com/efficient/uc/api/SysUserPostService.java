package com.efficient.uc.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.efficient.uc.model.dto.SysUserPostDTO;
import com.efficient.uc.model.dto.SysUserPostListDTO;
import com.efficient.uc.model.entity.SysUserPost;
import com.efficient.uc.model.vo.SysUserPostVO;

/**
 * <p>
 * 用户职位信息 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
public interface SysUserPostService extends IService<SysUserPost> {
    /***
     * 新增
     */
    Result<SysUserPost> save(SysUserPostDTO dto);

    /**
     * 详情
     */
    Result<SysUserPostVO> findById(String id);

    /**
     * 修改
     */
    Result<Boolean> update(SysUserPostDTO dto);

    /**
     * 删除
     */
    Result<Boolean> delete(String id);

    /**
     * 列表查询
     */
    Page<SysUserPostVO> list(SysUserPostListDTO dto);

    SysUserPost getMainByUserId(String userId);

}
