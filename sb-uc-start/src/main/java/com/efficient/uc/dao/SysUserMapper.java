package com.efficient.uc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.efficient.uc.model.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 * 用户信息 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    SysUser getByZwddId(@Param("zwddId") String zwddId);

    SysUser findByAccount(@Param("account") String account);

    void unLockUser(@Param("userId") String userId);

    void lockUser(@Param("userId") String userId, @Param("unLockTime") Date unLockTime);
}
