package com.efficient.uc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.efficient.uc.model.dto.TreeDTO;
import com.efficient.uc.model.entity.SysUnit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 机构数据 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@Mapper
public interface SysUnitMapper extends BaseMapper<SysUnit> {

    SysUnit findLastUnitByLength(@Param("unitLevelCount") Integer unitLevelCount);

    SysUnit findLastUnitByParentId(@Param("parentId") String parentId);

    SysUnit findLastUnitByParentOrgCode(@Param("parentOrgCode") String parentOrgCode);

    String getBelongById(@Param("id") String id);

    SysUnit getByOrgCode(@Param("organizationCode") String organizationCode);

    SysUnit getUnitByDeptId(@Param("deptId") String deptId);

    SysUnit findByOrgCode(@Param("orgCode") String orgCode);

    List<SysUnit> findLevelCodeAndNext(@Param("dto") TreeDTO dto);

    Integer findNextCountByLevelCode(@Param("levelCode") String levelCode);

    List<SysUnit> findNextAndNextGroup(@Param("levelCode") String levelCode);

    List<SysUnit> findByLevelCodeLike(@Param("levelCode") String levelCode);
}
