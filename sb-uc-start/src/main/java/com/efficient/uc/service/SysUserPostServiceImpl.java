package com.efficient.uc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.uc.api.SysUnitService;
import com.efficient.uc.api.SysUserPostService;
import com.efficient.uc.api.SysUserService;
import com.efficient.uc.dao.SysUserPostMapper;
import com.efficient.uc.model.converter.SysUserPostConverter;
import com.efficient.uc.model.dto.SysUserPostDTO;
import com.efficient.uc.model.dto.SysUserPostListDTO;
import com.efficient.uc.model.entity.SysUnit;
import com.efficient.uc.model.entity.SysUser;
import com.efficient.uc.model.entity.SysUserPost;
import com.efficient.uc.model.vo.SysUserPostVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 用户职位信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@Service
public class SysUserPostServiceImpl extends ServiceImpl<SysUserPostMapper, SysUserPost> implements SysUserPostService {

    @Autowired
    private SysUserPostConverter sysUserPostConverter;
    @Autowired
    private SysUserPostMapper sysUserPostMapper;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUnitService sysUnitService;

    @Override
    public Result<SysUserPost> save(SysUserPostDTO dto) {
        SysUserPost entity = sysUserPostConverter.dto2Entity(dto);
        SysUser sysUser = sysUserService.getById(entity.getUserId());
        if (ObjectUtil.isNull(sysUser)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        entity.setUserId(sysUser.getId());
        SysUnit sysUnit = sysUnitService.getById(entity.getUnitId());
        if (ObjectUtil.isNull(sysUnit)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        entity.setUnitId(sysUnit.getId());
        entity.setUnitLevelCode(sysUnit.getLevelCode());
        entity.setJoinDate(DateUtil.date());
        if (StrUtil.equals(sysUnit.getUnitType(), "3")) {
            SysUnit unitByOrgCode = sysUnitService.getUnitByOrgCode(sysUnit.getOrgCode());
            entity.setDeptId(unitByOrgCode.getId());
            entity.setDeptLevelCode(unitByOrgCode.getLevelCode());
        } else {
            entity.setDeptId(sysUnit.getId());
            entity.setDeptLevelCode(sysUnit.getLevelCode());
        }
        entity.setIsDelete(0);
        entity.setCreateTime(DateUtil.date());
        entity.setUpdateTime(DateUtil.date());
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<SysUserPostVO> findById(String id) {
        SysUserPost entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        SysUserPostVO vo = sysUserPostConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(SysUserPostDTO dto) {
        SysUserPost entity = sysUserPostConverter.dto2Entity(dto);
        if (StrUtil.isBlank(entity.getId())) {
            return Result.build(ResultEnum.PARA_ERROR);
        }
        SysUserPost sysUserPost = sysUserPostMapper.selectById(entity.getId());
        if (Objects.isNull(sysUserPost)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        SysUser sysUser = sysUserService.getById(entity.getUserId());
        if (ObjectUtil.isNull(sysUser)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        entity.setUserId(sysUser.getId());
        SysUnit sysUnit = sysUnitService.getById(entity.getUnitId());
        if (ObjectUtil.isNull(sysUnit)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        entity.setUnitId(sysUnit.getId());
        entity.setUnitLevelCode(sysUnit.getLevelCode());
        entity.setJoinDate(DateUtil.date());
        if (StrUtil.equals(sysUnit.getUnitType(), "3")) {
            SysUnit unitByOrgCode = sysUnitService.getUnitByOrgCode(sysUnit.getOrgCode());
            entity.setDeptId(unitByOrgCode.getId());
            entity.setDeptLevelCode(unitByOrgCode.getLevelCode());
        } else {
            entity.setDeptId(sysUnit.getId());
            entity.setDeptLevelCode(sysUnit.getLevelCode());
        }
        entity.setUpdateTime(DateUtil.date());
        boolean flag = this.updateById(entity);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SysUserPostVO> list(SysUserPostListDTO dto) {
        LambdaQueryWrapper<SysUserPost> queryWrapper = new LambdaQueryWrapper<>(SysUserPost.class);
        final Page<SysUserPost> page = sysUserPostMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<SysUserPostVO> voList = new ArrayList<>();
        List<SysUserPost> records = page.getRecords();
        Page<SysUserPostVO> newPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        newPage.setRecords(voList);
        if (CollUtil.isEmpty(records)) {
            return newPage;
        }
        records.forEach(et -> {
            SysUserPostVO vo = sysUserPostConverter.entity2Vo(et);
            voList.add(vo);
        });
        return newPage;
    }

    @Override
    public SysUserPost getMainByUserId(String userId) {
        return sysUserPostMapper.getMainByUserId(userId);
    }
}

