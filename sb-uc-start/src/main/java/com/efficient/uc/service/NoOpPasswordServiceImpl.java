package com.efficient.uc.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.efficient.common.api.PasswordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Component;

/**
 * 无认证模块时的密码服务实现
 * 提供简单的 MD5 加密作为降级方案
 * 
 * <AUTHOR>
 * @since 2024/7/28
 */
@Component
@ConditionalOnMissingBean(PasswordService.class)
@Slf4j
public class NoOpPasswordServiceImpl implements PasswordService {
    
    @Override
    public String encrypt(String password) {
        log.debug("Using simple MD5 encryption as fallback");
        return SecureUtil.md5(password);
    }
    
    @Override
    public String createPassword(String password) {
        log.debug("Using simple MD5 encryption for password creation");
        return SecureUtil.md5(password);
    }
    
    @Override
    public boolean checkEncrypt(String cryptPassword, String password) {
        String encrypted = SecureUtil.md5(password);
        return StrUtil.equals(cryptPassword, encrypted);
    }
}
