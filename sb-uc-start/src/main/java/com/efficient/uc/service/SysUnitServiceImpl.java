package com.efficient.uc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.common.entity.TreeNode;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.common.util.TreeUtil;
import com.efficient.uc.api.SysUnitService;
import com.efficient.uc.constant.UserCenterConstant;
import com.efficient.uc.constant.UserCenterResultEnum;
import com.efficient.uc.dao.SysUnitMapper;
import com.efficient.uc.model.converter.SysUnitConverter;
import com.efficient.uc.model.dto.SysUnitDTO;
import com.efficient.uc.model.dto.SysUnitListDTO;
import com.efficient.uc.model.dto.TreeDTO;
import com.efficient.uc.model.entity.SysUnit;
import com.efficient.uc.model.vo.SysUnitVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 机构数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@Service
@Slf4j
public class SysUnitServiceImpl extends ServiceImpl<SysUnitMapper, SysUnit> implements SysUnitService {

    @Autowired
    private SysUnitConverter sysUnitConverter;
    @Autowired
    private SysUnitMapper sysUnitMapper;

    @Override
    public Result<SysUnit> save(SysUnitDTO dto) {
        SysUnit entity = sysUnitConverter.dto2Entity(dto);
        entity.setId(null);
        entity.setOrgCode(IdUtil.fastSimpleUUID());
        if (StrUtil.equals(entity.getParentId(), UserCenterConstant.ZERO_PARENT_ID)) {
            entity.setParentOrgCode(null);
        } else {
            SysUnit sysUnit = sysUnitMapper.selectById(entity.getParentId());
            if (ObjectUtil.isNull(sysUnit)) {
                return Result.build(ResultEnum.DATA_NOT_EXIST);
            }
            entity.setParentOrgCode(sysUnit.getOrgCode());
        }
        String levelCode = createLevelCodeByCode(entity.getParentOrgCode());
        entity.setLevelCode(levelCode);

        SysUnit byOrgCode = getByOrgCode(entity.getParentOrgCode());
        String belong;
        if (Objects.nonNull(byOrgCode)) {
            belong = byOrgCode.getBelong() + "-" + entity.getName();
        } else {
            belong = entity.getName();
        }
        entity.setBelong(belong);

        entity.setIsDelete(0);
        entity.setCreateTime(DateUtil.date());
        entity.setUpdateTime(DateUtil.date());
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<SysUnitVO> findById(String id) {
        SysUnit entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        SysUnitVO vo = sysUnitConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(SysUnitDTO dto) {
        SysUnit entity = sysUnitConverter.dto2Entity(dto);
        if (StrUtil.isBlank(entity.getId())) {
            return Result.build(ResultEnum.PARA_ERROR);
        }
        SysUnit sysUnit = sysUnitMapper.selectById(entity.getId());
        if (ObjectUtil.isNull(sysUnit)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        entity.setParentId(null);
        entity.setLevelCode(null);
        entity.setOrgCode(null);
        entity.setParentOrgCode(null);
        entity.setBelong(null);
        entity.setUpdateTime(DateUtil.date());
        boolean flag = this.updateById(entity);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        SysUnit sysUnit = sysUnitMapper.selectById(id);
        if (ObjectUtil.isNull(sysUnit)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        LambdaQueryWrapper<SysUnit> queryWrapper = new LambdaQueryWrapper<>(SysUnit.class);
        queryWrapper.likeRight(SysUnit::getLevelCode, sysUnit.getLevelCode());
        queryWrapper.ne(SysUnit::getLevelCode, sysUnit.getLevelCode());
        List<SysUnit> sysUnitList = sysUnitMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(sysUnitList)) {
            return Result.build(UserCenterResultEnum.EXIST_CHILD_DATA);
        }
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SysUnitVO> list(SysUnitListDTO dto) {
        LambdaQueryWrapper<SysUnit> queryWrapper = new LambdaQueryWrapper<>(SysUnit.class);
        final Page<SysUnit> page = sysUnitMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<SysUnitVO> voList = new ArrayList<>();
        List<SysUnit> records = page.getRecords();
        Page<SysUnitVO> newPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        newPage.setRecords(voList);
        if (CollUtil.isEmpty(records)) {
            return newPage;
        }
        records.forEach(et -> {
            SysUnitVO vo = sysUnitConverter.entity2Vo(et);
            voList.add(vo);
        });
        return newPage;
    }

    @Override
    public synchronized String createLevelCode(String parentId) {
        SysUnit sysUnit = this.getById(parentId);
        if (Objects.isNull(sysUnit)) {
            SysUnit sysUnitLast = sysUnitMapper.findLastUnitByLength(UserCenterConstant.UNIT_LEVEL_COUNT);
            if (Objects.isNull(sysUnitLast)) {
                return UserCenterConstant.UNIT_TOP_LEVEL;
            } else {
                String levelCode = sysUnitLast.getLevelCode();
                return getNextLevelCode(levelCode);
            }
        } else {
            SysUnit sysUnitLast = sysUnitMapper.findLastUnitByParentId(parentId);
            if (Objects.isNull(sysUnitLast)) {
                return sysUnit.getLevelCode() + UserCenterConstant.UNIT_SUB_LEVEL;
            } else {
                String levelCode = sysUnitLast.getLevelCode();
                return getNextLevelCode(levelCode);
            }
        }
    }

    @Override
    public synchronized String createLevelCodeByCode(String orgCode) {
        SysUnit sysUnit = sysUnitMapper.findByOrgCode(orgCode);
        if (Objects.isNull(sysUnit)) {
            SysUnit sysUnitLast = sysUnitMapper.findLastUnitByLength(UserCenterConstant.UNIT_LEVEL_COUNT);
            if (Objects.isNull(sysUnitLast)) {
                return UserCenterConstant.UNIT_TOP_LEVEL;
            } else {
                String levelCode = sysUnitLast.getLevelCode();
                return getNextLevelCode(levelCode);
            }
        } else {
            SysUnit sysUnitLast = sysUnitMapper.findLastUnitByParentOrgCode(orgCode);
            if (Objects.isNull(sysUnitLast)) {
                return sysUnit.getLevelCode() + UserCenterConstant.UNIT_SUB_LEVEL;
            } else {
                String levelCode = sysUnitLast.getLevelCode();
                return getNextLevelCode(levelCode);
            }
        }
    }

    @Override
    public String getBelongById(String id) {
        return sysUnitMapper.getBelongById(id);
    }

    @Override
    public SysUnit getByOrgCode(String organizationCode) {
        return sysUnitMapper.getByOrgCode(organizationCode);
    }

    @Override
    public SysUnit getUnitByDeptId(String deptId) {
        return sysUnitMapper.getUnitByDeptId(deptId);
    }

    @Override
    public SysUnit getUnitByOrgCode(String orgCode) {
        boolean flag = true;
        SysUnit sysUnit = null;
        String findOrgCode = orgCode;
        while (flag) {
            SysUnit byOrgCode = this.getByOrgCode(findOrgCode);
            if (Objects.isNull(byOrgCode)) {
                flag = false;
            } else if (StrUtil.equals(byOrgCode.getUnitType(), "2")) {
                flag = false;
                sysUnit = byOrgCode;
            } else {
                findOrgCode = byOrgCode.getParentOrgCode();
            }
        }
        return sysUnit;
    }

    @Override
    public String getNextLevelCode(String levelCode) {
        String substring = levelCode.substring(levelCode.length() - 3);
        int next = Integer.parseInt(substring) + 1;
        String format = String.format("%03d", next);
        return levelCode.substring(0, levelCode.length() - 3) + format;
    }

    @Override
    public Result<TreeNode> tree(TreeDTO dto) {
        List<SysUnit> sysUnits = sysUnitMapper.findLevelCodeAndNext(dto);
        if (CollUtil.isEmpty(sysUnits)) {
            return Result.ok(TreeNode.builder().build());
        }
        List<TreeNode> treeNodes = this.buildTreeNode(dto.getCode(), sysUnits, true);
        TreeNode tree = TreeUtil.createTree(treeNodes);

        return Result.ok(tree);
    }

    @Override
    public List<TreeNode> buildTreeNode(String rootCode, List<SysUnit> sysUnitList, boolean checkIsLeaf) {
        List<TreeNode> treeNodeList = new ArrayList<>();
        if (CollUtil.isEmpty(sysUnitList)) {
            return treeNodeList;
        }
        Map<String, String> codeMap;
        if (checkIsLeaf) {
            List<SysUnit> childList = sysUnitMapper.findNextAndNextGroup(rootCode);
            codeMap = childList.stream().collect(Collectors.toMap(SysUnit::getParentOrgCode, SysUnit::getOrgCode, (k1, k2) -> k1));
        } else {
            codeMap = new HashMap<>();
        }

        sysUnitList.stream().filter(et -> StrUtil.isNotBlank(et.getLevelCode())).forEach(et -> {
            TreeNode build = TreeNode.builder().build();
            build.setId(et.getId());
            // build.setParentId(et.getParentId());
            String levelCode = et.getLevelCode();
            if (levelCode.length() > 3) {
                build.setParentCode(levelCode.substring(0, levelCode.length() - 3));
            } else {
                build.setParentCode("-1");
            }
            if (StrUtil.equals(rootCode, et.getLevelCode())) {
                build.setIsRoot(true);
                if (checkIsLeaf) {
                    build.setIsLeaf(sysUnitList.size() <= 1);
                }
            } else {
                build.setIsRoot(false);
                if (checkIsLeaf) {
                    build.setIsLeaf(Objects.isNull(codeMap.get(et.getOrgCode())));
                }
            }

            build.setCode(levelCode);
            build.setName(et.getName());
            build.setType(et.getUnitType());
            build.setOrder(et.getSort());
            treeNodeList.add(build);
        });

        return treeNodeList;
    }

    @Override
    public Result<TreeNode> treeAll(TreeDTO dto) {
        List<SysUnit> sysUnitList = sysUnitMapper.findByLevelCodeLike(dto.getCode());
        if (CollUtil.isEmpty(sysUnitList)) {
            return Result.ok(TreeNode.builder().build());
        }
        List<TreeNode> treeNodes = this.buildTreeNode(dto.getCode(), sysUnitList, false);
        TreeNode tree = TreeUtil.createTree(treeNodes);
        return Result.ok(tree);
    }
}
