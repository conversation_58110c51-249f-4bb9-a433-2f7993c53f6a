package com.efficient.uc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.auth.util.AuthUtil;
import com.efficient.common.constant.DbConstant;
import com.efficient.common.result.Result;
import com.efficient.common.result.ResultEnum;
import com.efficient.file.api.SysFileInfoService;
import com.efficient.file.model.entity.SysFileInfo;
import com.efficient.file.model.vo.FileVO;
import com.efficient.uc.api.SysUserService;
import com.efficient.uc.constant.UserCenterResultEnum;
import com.efficient.uc.dao.SysUserMapper;
import com.efficient.uc.model.converter.SysUserConverter;
import com.efficient.uc.model.dto.SysUserDTO;
import com.efficient.uc.model.dto.SysUserListDTO;
import com.efficient.uc.model.entity.SysUser;
import com.efficient.uc.model.excel.SysUserExcel;
import com.efficient.uc.model.vo.SysUserVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.charset.Charset;
import java.util.*;

import static com.efficient.file.service.SysFileInfoServiceImpl.entity2VO;

/**
 * <p>
 * 用户信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private SysUserConverter sysUserConverter;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysFileInfoService fileInfoService;
    @Autowired
    private AuthUtil authUtil;

    @Override
    public Result<SysUser> save(SysUserDTO dto) {
        SysUser entity = sysUserConverter.dto2Entity(dto);
        entity.setId(null);
        if (StrUtil.isBlank(entity.getPassword())) {
            return Result.build(ResultEnum.PARA_ERROR);
        }
        entity.setPassword(authUtil.encrypt(entity.getPassword()));
        if (!checkZwddIdUnique(entity)) {
            return Result.build(UserCenterResultEnum.EXIST_ZWDD_ID);
        }
        if (!checkAccountUnique(entity)) {
            return Result.build(UserCenterResultEnum.EXIST_ACCOUNT);
        }
        if (!checkIdCardUnique(entity)) {
            return Result.build(UserCenterResultEnum.EXIST_ID_CARD);
        }
        if (!checkPhoneUnique(entity)) {
            return Result.build(UserCenterResultEnum.EXIST_PHONE);
        }
        entity.setIsDelete(0);
        entity.setCreateTime(DateUtil.date());
        entity.setUpdateTime(DateUtil.date());
        boolean flag = this.save(entity);
        return Result.ok(entity);
    }

    @Override
    public Result<SysUserVO> findById(String id) {
        SysUser entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        SysUserVO vo = sysUserConverter.entity2Vo(entity);
        return Result.ok(vo);
    }

    @Override
    public Result<Boolean> update(SysUserDTO dto) {
        SysUser entity = sysUserConverter.dto2Entity(dto);
        if (StrUtil.isBlank(entity.getId())) {
            return Result.build(ResultEnum.PARA_ERROR);
        }
        SysUser sysUser = sysUserMapper.selectById(entity.getId());
        if (ObjectUtil.isNull(sysUser)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        entity.setPassword(null);
        if (!checkZwddIdUnique(entity)) {
            return Result.build(UserCenterResultEnum.EXIST_ZWDD_ID);
        }
        if (!checkAccountUnique(entity)) {
            return Result.build(UserCenterResultEnum.EXIST_ACCOUNT);
        }
        if (!checkIdCardUnique(entity)) {
            return Result.build(UserCenterResultEnum.EXIST_ID_CARD);
        }
        if (!checkPhoneUnique(entity)) {
            return Result.build(UserCenterResultEnum.EXIST_PHONE);
        }
        entity.setUpdateTime(DateUtil.date());
        boolean flag = this.updateById(entity);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Result<Boolean> delete(String id) {
        boolean flag = this.removeById(id);
        return flag ? Result.ok() : Result.fail();
    }

    @Override
    public Page<SysUserVO> list(SysUserListDTO dto) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>(SysUser.class);
        final Page<SysUser> page = sysUserMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), queryWrapper);
        List<SysUserVO> voList = new ArrayList<>();
        List<SysUser> records = page.getRecords();
        Page<SysUserVO> newPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        newPage.setRecords(voList);
        if (CollUtil.isEmpty(records)) {
            return newPage;
        }
        records.forEach(et -> {
            SysUserVO vo = sysUserConverter.entity2Vo(et);
            voList.add(vo);
        });
        return newPage;
    }

    @Override
    @Transactional
    public Result<Boolean> importExcel(String fileId) {
        SysFileInfo sysFileInfo = fileInfoService.getById(fileId);
        if (ObjectUtil.isNull(sysFileInfo)) {
            return Result.fail();
        }
        String filePath = sysFileInfo.getFilePath();
        File file = new File(filePath);
        List<SysUser> entityList = CollUtil.newArrayList();
        StringJoiner message = new StringJoiner(StrUtil.COMMA);
        EasyExcel.read(file.getAbsolutePath(), SysUserExcel.class, new PageReadListener<SysUserExcel>(importList -> {
            for (SysUserExcel excel : importList) {
                SysUser entity = sysUserConverter.excel2Entity(excel);
                entity.setPassword(authUtil.encrypt(entity.getPassword()));
                if (!checkZwddIdUnique(entity)) {
                    message.add(UserCenterResultEnum.EXIST_ZWDD_ID.getMsg() + StrUtil.COLON + entity.getZwddId());
                }
                if (!checkAccountUnique(entity)) {
                    message.add(UserCenterResultEnum.EXIST_ACCOUNT.getMsg() + StrUtil.COLON + entity.getAccount());
                }
                if (!checkIdCardUnique(entity)) {
                    message.add(UserCenterResultEnum.EXIST_ID_CARD.getMsg() + StrUtil.COLON + entity.getIdCard());
                }
                if (!checkPhoneUnique(entity)) {
                    message.add(UserCenterResultEnum.EXIST_PHONE.getMsg() + StrUtil.COLON + entity.getPhone());
                }
                entityList.add(entity);
            }
        })).sheet().doRead();
        if (StrUtil.isNotBlank(message.toString())) {
            return Result.build(ResultEnum.FAILED.getCode(), message.toString());
        }
        if (CollUtil.isNotEmpty(entityList)) {
            this.saveBatch(entityList, DbConstant.BATCH_SIZE);
        }
        return Result.ok();
    }

    @Override
    public Result<FileVO> exportExcel(SysUserListDTO dto) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>(SysUser.class);
        List<SysUser> entityList = sysUserMapper.selectList(queryWrapper);
        List<SysUserExcel> exportList = CollUtil.newArrayList();
        for (SysUser entity : entityList) {
            SysUserExcel excel = sysUserConverter.entity2Excel(entity);
            exportList.add(excel);
        }
        String fileName = "用户信息" + ExcelTypeEnum.XLSX.getValue();
        File downFile = fileInfoService.getDownPath(fileName);
        EasyExcel.write(downFile.getAbsolutePath(), SysUserExcel.class)
                .excelType(ExcelTypeEnum.XLSX)
                .charset(Charset.defaultCharset())
                .sheet()
                .doWrite(exportList);
        SysFileInfo fileInfo = fileInfoService.saveDownFile(downFile, null, fileName, "exportExcel");
        return Result.ok(entity2VO(fileInfo));
    }

    @Override
    public SysUser getByZwddId(String zwddId) {
        return sysUserMapper.getByZwddId(zwddId);
    }

    @Override
    public SysUser getByAccount(String account) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>(SysUser.class);
        queryWrapper.eq(SysUser::getAccount, account);
        queryWrapper.orderByAsc(SysUser::getId);
        queryWrapper.last(DbConstant.LIMIT_ONE);
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean unLockUser(String userId) {
        sysUserMapper.unLockUser(userId);
        return true;
    }

    @Override
    public boolean lockUser(String userId, Date unLockTime) {
        sysUserMapper.lockUser(userId, unLockTime);
        return true;
    }

    @Override
    public Result<Boolean> resetPassword(String userId, String oldPassword, String newPassword, Boolean isReset) {
        SysUser sysUser = this.getById(userId);
        if (Objects.isNull(sysUser)) {
            return Result.build(ResultEnum.DATA_NOT_EXIST);
        }
        if (!isReset) {
            if (StrUtil.isBlank(oldPassword)) {
                return Result.build(UserCenterResultEnum.OLD_PASSWORD_NOT_NULL);
            }
            if (!StrUtil.equals(authUtil.createPassword(oldPassword), sysUser.getPassword())) {
                return Result.build(UserCenterResultEnum.OLD_PASSWORD_NOT_EQ);
            }
        }
        sysUser.setPassword(authUtil.createPassword(newPassword));
        this.updateById(sysUser);
        return Result.ok();
    }

    private Boolean checkZwddIdUnique(SysUser sysUser) {
        if (StrUtil.isNotEmpty(sysUser.getZwddId())) {
            String userId = ObjectUtil.isNull(sysUser.getId()) ? StrUtil.EMPTY : sysUser.getId();
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>(SysUser.class);
            queryWrapper.eq(SysUser::getZwddId, sysUser.getZwddId());
            SysUser user = this.baseMapper.selectOne(queryWrapper);
            return !ObjectUtil.isNotNull(user) || StrUtil.equals(user.getId(), userId);
        }
        return true;
    }

    private Boolean checkAccountUnique(SysUser sysUser) {
        if (StrUtil.isNotEmpty(sysUser.getAccount())) {
            String userId = ObjectUtil.isNull(sysUser.getId()) ? StrUtil.EMPTY : sysUser.getId();
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>(SysUser.class);
            queryWrapper.eq(SysUser::getAccount, sysUser.getAccount());
            SysUser user = this.baseMapper.selectOne(queryWrapper);
            return !ObjectUtil.isNotNull(user) || StrUtil.equals(user.getId(), userId);
        }
        return true;
    }

    private Boolean checkIdCardUnique(SysUser sysUser) {
        if (StrUtil.isNotEmpty(sysUser.getIdCard())) {
            String userId = ObjectUtil.isNull(sysUser.getId()) ? StrUtil.EMPTY : sysUser.getId();
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>(SysUser.class);
            queryWrapper.eq(SysUser::getIdCard, sysUser.getIdCard());
            SysUser user = this.baseMapper.selectOne(queryWrapper);
            return !ObjectUtil.isNotNull(user) || StrUtil.equals(user.getId(), userId);
        }
        return true;
    }

    private Boolean checkPhoneUnique(SysUser sysUser) {
        if (StrUtil.isNotEmpty(sysUser.getPhone())) {
            String userId = ObjectUtil.isNull(sysUser.getId()) ? StrUtil.EMPTY : sysUser.getId();
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>(SysUser.class);
            queryWrapper.eq(SysUser::getPhone, sysUser.getPhone());
            SysUser user = this.baseMapper.selectOne(queryWrapper);
            return !ObjectUtil.isNotNull(user) || StrUtil.equals(user.getId(), userId);
        }
        return true;
    }
}
