package com.efficient.uc.service;

import com.efficient.auth.api.AuthService;
import com.efficient.auth.constant.AuthConstant;
import com.efficient.auth.model.entity.UserAuthInfo;
import com.efficient.auth.util.AuthUtil;
import com.efficient.cache.api.CacheUtil;
import com.efficient.common.auth.UserTicket;
import com.efficient.common.result.Result;
import com.efficient.uc.api.SysUserPostService;
import com.efficient.uc.api.SysUserService;
import com.efficient.uc.dao.SysUserMapper;
import com.efficient.uc.model.entity.SysUser;
import com.efficient.uc.model.entity.SysUserPost;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/4/25 16:55
 */
@ConditionalOnProperty(name = "com.efficient.auth.authService", havingValue = "default", matchIfMissing = true)
@Service
public class EfficientAuthService implements AuthService {
    @Autowired
    private AuthUtil authUtil;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysUserPostService sysUserPostService;
    @Autowired
    private CacheUtil cacheUtil;

    @Override
    public UserAuthInfo getUserByAccount(String account) {
        SysUser sysUser = sysUserMapper.findByAccount(account);
        return this.findUserAuthInfo(sysUser);
    }

    @Override
    public UserAuthInfo getUserByZwddId(String zwddId) {
        SysUser sysUser = sysUserMapper.getByZwddId(zwddId);
        return this.findUserAuthInfo(sysUser);
    }

    @Override
    public UserAuthInfo getUserByUserId(String userId) {
        SysUser sysUser = sysUserService.getById(userId);
        return this.findUserAuthInfo(sysUser);
    }

    @Override
    public boolean unLockUser(String userId) {
        cacheUtil.removeCache(AuthConstant.AUTH_CACHE, AuthConstant.LOGIN_FAIL_CACHE + userId);
        return sysUserService.unLockUser(userId);
    }

    @Override
    public boolean lockUser(String userId, Date unLockTime) {
        return sysUserService.lockUser(userId, unLockTime);
    }

    @Override
    public Result<UserTicket> loadUserTicket(UserAuthInfo userAuthInfo) {
        UserTicket userTicket = new UserTicket();
        userTicket.setUserId(userAuthInfo.getUserId());
        userTicket.setZwddId(userAuthInfo.getZwddId());
        userTicket.setAccount(userAuthInfo.getAccount());
        userTicket.setUsername(userAuthInfo.getUsername());
        SysUserPost sysUserPost = sysUserPostService.getMainByUserId(userAuthInfo.getUserId());
        if (Objects.nonNull(sysUserPost)) {
            userTicket.setUserUnitId(sysUserPost.getUnitId());
            userTicket.setUserUnitPost(sysUserPost.getPostName());
        }
        // 修改登录时间
        SysUser sysUser = sysUserService.getById(userAuthInfo.getUserId());
        sysUser.setLastLoginTime(new Date());
        sysUserService.updateById(sysUser);
        return Result.ok(userTicket);
    }

    private UserAuthInfo findUserAuthInfo(SysUser sysUser) {
        if (Objects.isNull(sysUser)) {
            return null;
        }

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setAccount(sysUser.getAccount());
        userAuthInfo.setPassword(sysUser.getPassword());
        userAuthInfo.setUsername(sysUser.getName());
        userAuthInfo.setUserId(sysUser.getId());
        userAuthInfo.setZwddId(sysUser.getZwddId());
        userAuthInfo.setUnLockTime(sysUser.getExpirationTime());
        return userAuthInfo;
    }
}
