package com.efficient.uc.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.entity.TreeNode;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.efficient.uc.api.SysUnitService;
import com.efficient.uc.model.dto.SysUnitDTO;
import com.efficient.uc.model.dto.SysUnitListDTO;
import com.efficient.uc.model.dto.TreeDTO;
import com.efficient.uc.model.entity.SysUnit;
import com.efficient.uc.model.vo.SysUnitVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 机构数据 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:29
 */
@RestController
@RequestMapping("/uc/sysUnit")
@Validated
@Api(tags = "机构数据")
@Permission
public class SysUnitController {

    @Autowired
    private SysUnitService sysUnitService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "机构数据")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = SysUnit.class)
    public Result<SysUnit> save(@Validated @RequestBody SysUnitDTO dto) {
        return sysUnitService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "机构数据")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = SysUnitVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SysUnitVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sysUnitService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "机构数据")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Boolean.class)
    public Result<Boolean> update(@Validated @RequestBody SysUnitDTO dto) {
        return sysUnitService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "机构数据")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sysUnitService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "机构数据")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = SysUnitVO.class)
    public Result<Page<SysUnitVO>> list(@Validated @RequestBody SysUnitListDTO dto) {
        Page<SysUnitVO> page = sysUnitService.list(dto);
        return Result.ok(page);
    }

    /**
     * 按层级查找机构树
     */
    // @Log(logOpt = LogEnum.QUERY, module = "机构数据")
    @PostMapping("/tree")
    @ApiOperation(value = "按层级查找机构树")
    public Result<TreeNode> tree(@Validated @RequestBody TreeDTO dto) {
        return sysUnitService.tree(dto);
    }

    /**
     * 按层级查找机构树
     */
    // @Log(logOpt = LogEnum.QUERY, module = "机构数据")
    @PostMapping("/treeAll")
    @ApiOperation(value = "获取全量机构树")
    public Result<TreeNode> treeAll(@Validated @RequestBody TreeDTO dto) {
        return sysUnitService.treeAll(dto);
    }
}
