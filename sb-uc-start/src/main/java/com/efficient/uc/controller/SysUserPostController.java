package com.efficient.uc.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.efficient.uc.api.SysUserPostService;
import com.efficient.uc.model.dto.SysUserPostDTO;
import com.efficient.uc.model.dto.SysUserPostListDTO;
import com.efficient.uc.model.entity.SysUserPost;
import com.efficient.uc.model.vo.SysUserPostVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 用户职位信息 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@RestController
@RequestMapping("/uc/sysUserPost")
@Validated
@Api(tags = "用户职位信息")
@Permission
public class SysUserPostController {

    @Autowired
    private SysUserPostService sysUserPostService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "用户职位信息")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = SysUserPost.class)
    public Result<SysUserPost> save(@Validated @RequestBody SysUserPostDTO dto) {
        return sysUserPostService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "用户职位信息")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = SysUserPostVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SysUserPostVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sysUserPostService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "用户职位信息")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Boolean.class)
    public Result<Boolean> update(@Validated @RequestBody SysUserPostDTO dto) {
        return sysUserPostService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "用户职位信息")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sysUserPostService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "用户职位信息")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = SysUserPostVO.class)
    public Result<Page<SysUserPostVO>> list(@Validated @RequestBody SysUserPostListDTO dto) {
        Page<SysUserPostVO> page = sysUserPostService.list(dto);
        return Result.ok(page);
    }
}
