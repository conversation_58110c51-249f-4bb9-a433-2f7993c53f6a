package com.efficient.uc.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.file.model.vo.FileVO;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.efficient.uc.api.SysUserService;
import com.efficient.uc.model.dto.SysUserDTO;
import com.efficient.uc.model.dto.SysUserListDTO;
import com.efficient.uc.model.entity.SysUser;
import com.efficient.uc.model.vo.SysUserVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 用户信息 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 14:25:28
 */
@RestController
@RequestMapping("/uc/sysUser")
@Validated
@Api(tags = "用户信息")
@Permission
public class SysUserController {

    @Autowired
    private SysUserService sysUserService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "用户信息")
    @PostMapping("/save")
    @ApiOperation(value = "保存", response = SysUser.class)
    public Result<SysUser> save(@Validated @RequestBody SysUserDTO dto) {
        return sysUserService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "用户信息")
    @GetMapping("/find")
    @ApiOperation(value = "详情", response = SysUserVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<SysUserVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sysUserService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "用户信息")
    @PostMapping("/update")
    @ApiOperation(value = "修改", response = Boolean.class)
    public Result<Boolean> update(@Validated @RequestBody SysUserDTO dto) {
        return sysUserService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "用户信息")
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return sysUserService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "用户信息")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = SysUserVO.class)
    public Result<Page<SysUserVO>> list(@Validated @RequestBody SysUserListDTO dto) {
        Page<SysUserVO> page = sysUserService.list(dto);
        return Result.ok(page);
    }

    /**
     * 导入
     */
    @Log(logOpt = LogEnum.IMPORT, module = "用户信息表")
    @GetMapping("/importExcel")
    @ApiOperation(value = "导入", response = Boolean.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "fileId", required = true)
    })
    public Result<Boolean> importExcel(@RequestParam(name = "fileId") String fileId) {
        return sysUserService.importExcel(fileId);
    }

    /**
     * 导出
     */
    @Log(logOpt = LogEnum.EXPORT, module = "用户信息表")
    @GetMapping("/exportExcel")
    @ApiOperation(value = "导出", response = FileVO.class)
    public Result<FileVO> exportExcel(@RequestBody SysUserListDTO dto) {
        return sysUserService.exportExcel(dto);
    }

    /**
     * 修改密码
     */
    @Log(logOpt = LogEnum.UPDATE, module = "用户信息")
    @GetMapping("/resetPassword")
    @ApiOperation(value = "修改密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "数据唯一标识", required = true),
            @ApiImplicitParam(name = "oldPassword", value = "旧密码", required = false),
            @ApiImplicitParam(name = "newPassword", value = "新密码", required = true),
            @ApiImplicitParam(name = "isReset", value = "是否重置，true-旧密码非必填，false-旧密码必填", required = true)
    })
    public Result<Boolean> resetPassword(
            @NotBlank(message = "userId 不能为空") @RequestParam(name = "userId") String userId,
            @RequestParam(name = "oldPassword") String oldPassword,
            @NotBlank(message = "newPassword 不能为空") @RequestParam(name = "newPassword") String newPassword,
            @NotNull(message = "isReset 不能为空") @RequestParam(name = "isReset") Boolean isReset
    ) {
        return sysUserService.resetPassword(userId, oldPassword, newPassword, isReset);
    }

    /**
     * 解锁用户
     */
    @Log(logOpt = LogEnum.UPDATE, module = "用户信息")
    @GetMapping("/unLockUser")
    @ApiOperation(value = "解锁用户")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> unLockUser(
            @NotBlank(message = "userId 不能为空") @RequestParam(name = "userId") String userId
    ) {
        boolean b = sysUserService.unLockUser(userId);
        return b ? Result.ok() : Result.ok();
    }
}
