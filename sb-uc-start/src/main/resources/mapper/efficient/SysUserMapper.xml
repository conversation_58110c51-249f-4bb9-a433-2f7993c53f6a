<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.efficient.system.dao.SysUserMapper">
    <update id="unLockUser">
        update efficient_sys_user
        set expiration_time = null
        where id = #{userId}
    </update>
    <update id="lockUser">
        update efficient_sys_user
        set expiration_time = #{unLockTime}
        where id = #{userId}
    </update>

    <select id="getByZwddId" resultType="com.efficient.uc.model.entity.SysUser">
        select *
        from efficient_sys_user
        where zwdd_id = #{zwddId}
        order by id limit 1
    </select>
    <select id="findByAccount" resultType="com.efficient.uc.model.entity.SysUser">
        select *
        from efficient_sys_user
        where account = #{account}
        order by id limit 1
    </select>
</mapper>

