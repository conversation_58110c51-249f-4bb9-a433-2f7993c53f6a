```xml

 <parent>
        <groupId>com.zenith</groupId>
        <artifactId>sb</artifactId>
        <version>2.0.2</version>
    </parent>

 <repositories>
 <repository>
     <id>central</id>
     <url>http://************:9999/repository/maven-public/</url>
     <releases>
         <enabled>true</enabled>
     </releases>
     <snapshots>
         <enabled>true</enabled>
     </snapshots>
 </repository>
 </repositories>
```

setting文件配置
[settings-76.xml](other%2Fsettings-76.xml)

```xml
<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<!--
 | This is the configuration file for Maven. It can be specified at two levels:
 |
 |  1. User Level. This settings.xml file provides configuration for a single user,
 |                 and is normally provided in ${user.home}/.m2/settings.xml.
 |
 |                 NOTE: This location can be overridden with the CLI option:
 |
 |                 -s /path/to/user/settings.xml
 |
 |  2. Global Level. This settings.xml file provides configuration for all Maven
 |                 users on a machine (assuming they're all using the same Maven
 |                 installation). It's normally provided in
 |                 ${maven.conf}/settings.xml.
 |
 |                 NOTE: This location can be overridden with the CLI option:
 |
 |                 -gs /path/to/global/settings.xml
 |
 | The sections in this sample file are intended to give you a running start at
 | getting the most out of your Maven installation. Where appropriate, the default
 | values (values used when the setting is not specified) are provided.
 |
 |-->
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
	<localRepository>F:/work/maven/repository</localRepository>
    
  <pluginGroups>
  </pluginGroups>
    
  <proxies>
  </proxies>
    
  <servers>
	 <server>
        <id>nexus</id>
        <username>read</username>
        <password>read</password>
    </server>
  </servers>

 
  <mirrors>
	  <mirror>
        <id>nexus</id>
        <mirrorOf>*</mirrorOf>
        <url>http://************:9999/repository/maven-public/</url>
        <layout>default</layout>
    </mirror>
	 
  </mirrors>
    
  <profiles>
    <profile>
        <id>nexus</id>
        <repositories>
            <repository>
                <id>central</id>
                <url>http://************:9999/repository/maven-public/</url>
                <releases><enabled>true</enabled></releases>
                <snapshots><enabled>true</enabled></snapshots>
            </repository>
        </repositories>
    </profile>

   
  </profiles>
    
    <activeProfiles>
    <activeProfile>nexus</activeProfile>
</activeProfiles>
</settings>

```

## 安装依赖

### aspose

```text
mvn -Dmaven.test.skip=true install

mvn install:install-file    -DgroupId=com.aspose -DartifactId=aspose-cells -Dversion=21.11 -Dpackaging=jar   -Dfile=./aspose-cells-21.11.jar

mvn install:install-file    -DgroupId=com.aspose -DartifactId=aspose-words -Dversion=21.6 -Dpackaging=jar   -Dfile=./aspose-words-21.11.0-jdk17.jar

mvn install:install-file    -DgroupId=com.hkws -DartifactId=artemis-http-client -Dversion=1.1.13.RELEASE -Dpackaging=jar   -Dfile=./artemis-http-client-1.1.13.RELEASE.jar
```

### YKZ

```text
mvn -Dmaven.test.skip=true install
mvn install:install-file    -DgroupId=com.dcqc -DartifactId=dcqc-uc-oauth-sdk -Dversion=3.0.0-RELEASE -Dpackaging=jar   -Dfile=./dcqc-uc-oauth-sdk-3.0.0-RELEASE.jar

mvn install:install-file    -DgroupId=com.alibaba -DartifactId=zwdd-sdk-java -Dversion=1.2.0 -Dpackaging=jar   -Dfile=./zwdd-sdk-java-1.2.0.jar
```

## 修改版本

```text
mvn versions:set -DnewVersion=
```
