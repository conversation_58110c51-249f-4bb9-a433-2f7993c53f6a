package com.efficient.swagger.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 添加配置
 * spring:
 * main:
 * allow-circular-references: true
 * allow-bean-definition-overriding: true
 * servlet:
 * multipart:
 * max-file-size: 1000MB
 * max-request-size: 10240MB
 * mvc:
 * servlet:
 * load-on-startup: 0
 * throw-exception-if-no-handler-found: true
 * path-match:
 * matching-strategy: ant_path_matcher
 *
 * <AUTHOR>
 * @date 2023/9/12
 */
@Configuration
@Slf4j
public class SwaggerWebMvcConfig implements WebMvcConfigurer {
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        // 使用 AntPathMatcher
        configurer.setPathMatcher(new AntPathMatcher());
    }
}
