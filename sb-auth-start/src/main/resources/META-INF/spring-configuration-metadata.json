{"groups": [{"name": "com.efficient.auth", "type": "com.efficient.auth.properties.AuthProperties", "sourceType": "com.efficient.auth.properties.AuthProperties"}], "properties": [{"name": "com.efficient.auth.systemIdField", "type": "java.lang.String", "description": "系统标识字段", "defaultValue": "systemId", "sourceType": "com.efficient.auth.properties.AuthProperties"}, {"name": "com.efficient.auth.authService", "type": "java.lang.String", "description": "系统认证方式，默认系统自带认证", "defaultValue": "default", "sourceType": "com.efficient.auth.properties.AuthProperties"}, {"name": "com.efficient.auth.permissionCheckType", "type": "java.lang.String", "description": "菜单权限校验实现类", "defaultValue": "default", "sourceType": "com.efficient.auth.properties.AuthProperties"}, {"name": "com.efficient.auth.userTicketClassName", "type": "java.lang.String", "description": "用户缓存类全限定名称，需要继承 com.efficient.common.auth.UserTicket", "defaultValue": "com.efficient.common.auth.UserTicket", "sourceType": "com.efficient.auth.properties.AuthProperties"}, {"name": "com.efficient.auth.whiteList", "type": "java.util.List", "description": "白名单", "defaultValue": "**swagger-resources**、/login、/captcha", "sourceType": "com.efficient.auth.properties.AuthProperties"}, {"name": "com.efficient.auth.tokenGet", "type": "java.util.List", "description": "get方式传递token", "defaultValue": "/video/play", "sourceType": "com.efficient.auth.properties.AuthProperties"}, {"name": "com.efficient.auth.tokenGetPost", "type": "java.util.List", "description": "post方式传递token", "defaultValue": null, "sourceType": "com.efficient.auth.properties.AuthProperties"}, {"name": "com.efficient.auth.login.captcha", "type": "java.lang.Bo<PERSON>an", "description": "是否需要验证码", "defaultValue": false, "sourceType": "com.efficient.auth.properties.LoginProperties"}, {"name": "com.efficient.auth.login.captchaRule", "type": "java.lang.String", "description": "验证码规则", "defaultValue": "23456789abcdefghjkmnpqrstuvwxyz", "sourceType": "com.efficient.auth.properties.LoginProperties"}, {"name": "com.efficient.auth.login.secret", "type": "java.lang.String", "description": "jwt secret", "defaultValue": "qwertyuiop0987654321", "sourceType": "com.efficient.auth.properties.LoginProperties"}, {"name": "com.efficient.auth.login.retry-count", "type": "java.lang.Integer", "description": "重试次数", "defaultValue": -1, "sourceType": "com.efficient.auth.properties.LoginProperties"}, {"name": "com.efficient.auth.login.retry-time", "type": "java.lang.Integer", "description": "重试间隔时间", "defaultValue": 30, "sourceType": "com.efficient.auth.properties.LoginProperties"}, {"name": "com.efficient.auth.login.lock-time", "type": "java.lang.Integer", "description": "登录失败过多，锁定时间，分钟", "defaultValue": 30, "sourceType": "com.efficient.auth.properties.LoginProperties"}, {"name": "com.efficient.auth.login.max-online", "type": "java.lang.Integer", "description": "同一个账号最多同时在线人数", "defaultValue": -1, "sourceType": "com.efficient.auth.properties.LoginProperties"}, {"name": "com.efficient.auth.login.token-live", "type": "java.lang.Integer", "description": "token最大存活时间，单位秒", "defaultValue": 3600, "sourceType": "com.efficient.auth.properties.LoginProperties"}, {"name": "com.efficient.auth.login.password-encrypt", "type": "java.lang.Bo<PERSON>an", "description": "密码是否加密传输", "defaultValue": false, "sourceType": "com.efficient.auth.properties.LoginProperties"}, {"name": "com.efficient.auth.login.enable-salt", "type": "java.lang.Bo<PERSON>an", "description": "密码是否加盐", "defaultValue": true, "sourceType": "com.efficient.auth.properties.LoginProperties"}, {"name": "com.efficient.auth.login.salt-value", "type": "java.lang.String", "description": "密码盐值", "defaultValue": "1809", "sourceType": "com.efficient.auth.properties.LoginProperties"}], "hints": [{"name": "com.efficient.auth.permissionCheckType", "values": [{"value": "default", "description": "默认实现"}, {"value": "custom", "description": "自定义实现，请先实现类 com.efficient.auth.interceptor.PermissionCheck"}]}, {"name": "com.efficient.auth.authService", "values": [{"value": "default", "description": "默认实现"}, {"value": "custom", "description": "自定义实现，请先实现类 com.efficient.auth.api.AuthService"}]}]}