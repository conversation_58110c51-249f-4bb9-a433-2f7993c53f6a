package com.efficient.auth.service;

import com.efficient.auth.util.AuthUtil;
import com.efficient.common.api.PasswordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

/**
 * 基于 AuthUtil 的密码服务实现
 * 提供完整的密码加密功能，包括盐值、Base64解码等
 * 
 * <AUTHOR>
 * @since 2024/7/28
 */
@Component
@ConditionalOnClass(AuthUtil.class)
public class AuthPasswordServiceImpl implements PasswordService {
    
    @Autowired
    private AuthUtil authUtil;
    
    @Override
    public String encrypt(String password) {
        return authUtil.encrypt(password);
    }
    
    @Override
    public String createPassword(String password) {
        return authUtil.createPassword(password);
    }
    
    @Override
    public boolean checkEncrypt(String cryptPassword, String password) {
        return authUtil.checkEncrypt(cryptPassword, password);
    }
}
