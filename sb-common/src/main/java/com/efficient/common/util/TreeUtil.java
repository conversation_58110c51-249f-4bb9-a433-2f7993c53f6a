package com.efficient.common.util;

import com.efficient.common.entity.TreeNode;

import java.util.*;

/**
 * 树形工具类
 *
 * <AUTHOR>
 * @since 2022/4/29 11:29
 */
public class TreeUtil {

    private static final Long LAST_ORDER = 9999L;

    /**
     * 获取书节点
     *
     * @param nodeList 节点集合
     * @return 树
     */
    public static TreeNode createTree(List<TreeNode> nodeList) {
        final List<TreeNode> listTree = createListTree(nodeList);
        return listTree.get(0);
    }

    public static List<TreeNode> createListTree(List<TreeNode> nodeList) {
        Map<String, List<TreeNode>> childrenMap = new HashMap<>();
        List<TreeNode> rootNodes = new ArrayList<>();

        // 遍历节点列表，分离根节点和构建 childrenMap
        for (TreeNode node : nodeList) {
            String parentCode = node.getParentCode();
            // 添加到根节点列表
            if (Boolean.TRUE.equals(node.getIsRoot())) {
                rootNodes.add(node);
            }
            childrenMap.computeIfAbsent(parentCode, k -> new ArrayList<>()).add(node);
        }

        // 构建树结构
        for (TreeNode rootNode : nodeList) {
            String code = rootNode.getCode();
            List<TreeNode> children = childrenMap.getOrDefault(code, Collections.emptyList());
            if (!children.isEmpty()) {
                children.sort(Comparator.comparing(TreeNode::getOrder, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(TreeNode::getCode));
                rootNode.setChildren(children);
                if (Objects.isNull(rootNode.getIsLeaf())) {
                    rootNode.setIsLeaf(false);
                }
            } else {
                if (Objects.isNull(rootNode.getIsLeaf())) {
                    rootNode.setIsLeaf(true);
                }
            }
        }
        // 对根节点进行排序
        rootNodes.sort(Comparator.comparing(TreeNode::getOrder, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(TreeNode::getCode));
        return rootNodes;
    }

    /**
     * 获取树形节点集合
     *
     * @param nodeList 节点集合
     * @return 树形节点集合
     */
    public static List<TreeNode> createListTreeOld(List<TreeNode> nodeList) {
        Map<String, List<TreeNode>> childrenMap = new HashMap<>();
        List<TreeNode> rootNodes = new ArrayList<>();

        // 遍历节点列表，分离根节点和构建 childrenMap
        for (TreeNode node : nodeList) {
            if (node.getIsRoot() != null && node.getIsRoot()) {
                rootNodes.add(node);
            } else {
                String parentCode = node.getParentCode();
                childrenMap.computeIfAbsent(parentCode, k -> new ArrayList<>()).add(node);
            }
        }

        // 对根节点进行排序
        rootNodes.sort(Comparator.comparing(TreeNode::getOrder, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(TreeNode::getCode));

        // 构建树结构
        for (TreeNode rootNode : rootNodes) {
            buildChildren(rootNode, childrenMap);
        }

        return rootNodes;
    }

    /**
     * 递归构建子节点
     *
     * @param parentNode  当前父节点
     * @param childrenMap 存储父节点ID对应的子节点列表
     */
    private static void buildChildren(TreeNode parentNode, Map<String, List<TreeNode>> childrenMap) {
        List<TreeNode> children = childrenMap.getOrDefault(parentNode.getCode(), Collections.emptyList());

        if (!children.isEmpty()) {
            // 对子节点排序
            children.sort(Comparator.comparing(TreeNode::getOrder, Comparator.nullsLast(Comparator.naturalOrder()))
                    .thenComparing(TreeNode::getCode));
            parentNode.setChildren(children);
            if (Objects.isNull(parentNode.getIsLeaf())) {
                parentNode.setIsLeaf(false);
            }

            // 递归构建每个子节点的子节点
            for (TreeNode child : children) {
                buildChildren(child, childrenMap);
            }
        } else {
            if (Objects.isNull(parentNode.getIsLeaf())) {
                parentNode.setIsLeaf(true);
            }
        }
    }
}
