package com.efficient.common.util;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2021/3/3 11:29
 */
public class JackSonUtil {
    public static final Logger LOGGER = LoggerFactory.getLogger(JackSonUtil.class);
    public static final ObjectMapper JSON = new ObjectMapper();

    static {
        JSON.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        JSON.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        JSON.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        // 允许出现特殊字符和转义符
        JSON.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        // 大小写脱敏 默认为false  需要改为true
        JSON.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
        // 将日期对象序列化为时间戳格式
        JSON.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, true);
        // 将字符数组序列化为 JSON 数组
        JSON.configure(SerializationFeature.WRITE_CHAR_ARRAYS_AS_JSON_ARRAYS, true);
        // 在 Map 中如果值为 null，是否序列化该键值对。
        // JSON.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, true);
        // 设置时区为 GMT+8
        JSON.setTimeZone(TimeZone.getTimeZone("GMT+8"));
    }

    public static String toJson(Object o) {
        if (Objects.isNull(o)) {
            return null;
        }
        try {
            return JSON.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            LOGGER.error("toJson 转换异常", e);
            return "";
        }
    }

    public static <T> T toObject(String str, Class<T> cs) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        try {
            return JSON.readValue(str, cs);
        } catch (JsonProcessingException e) {
            LOGGER.error("toObject 转换异常", e);
        }
        return null;
    }

    public static <T> List<T> toObjectList(String str, Class<T> clsChild) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        try {
            JavaType javaType = JSON.getTypeFactory().constructParametricType(List.class, clsChild);
            return JSON.readValue(str, javaType);
        } catch (JsonProcessingException e) {
            LOGGER.error("toObjectList 转换异常", e);
        }
        return null;
    }

    /**
     * 注意，此种方法返回的   java.util.LinkedHashMap
     *
     * @param str
     * @param <T>
     * @return
     */
    public static <T> List<T> toObjectListByType(String str) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        try {
            TypeReference<List<T>> type = new TypeReference<List<T>>() {
            };
            return JSON.readValue(str, type);
        } catch (JsonProcessingException e) {
            LOGGER.error("toObjectListByType 转换异常", e);
        }
        return null;
    }

    public static <T> Map<String, T> toObjectMap(String str, Class<T> vCls) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        try {
            JavaType javaType = JSON.getTypeFactory().constructParametricType(Map.class, String.class, vCls);
            return JSON.readValue(str, javaType);
        } catch (JsonProcessingException e) {
            LOGGER.error("toObjectMap 转换异常", e);
        }
        return null;
    }

    public static Map<String, Object> toMap(String str) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        try {
            return JSON.readValue(str, Map.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("toMap 转换异常", e);
        }
        return null;
    }

}
