package com.efficient.common.util;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.efficient.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2022/3/3 10:22
 */
@Slf4j
public class WebUtil {

    public static String getIP(HttpServletRequest request) {
        try {
            Assert.notNull(request, "HttpServletRequest is null");
            String ip = request.getHeader("X-Requested-For");
            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("X-Forwarded-For");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }

            if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }

            return StrUtil.isBlank(ip) ? null : ip.split(",")[0];
        } catch (Exception e) {
            log.error("获取IP地址异常：", e);
        }
        return CommonConstant.UNKNOWN;
    }
}
