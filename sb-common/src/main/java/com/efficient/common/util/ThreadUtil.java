package com.efficient.common.util;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.efficient.common.thread.LoggingCallerRunsPolicy;
import com.efficient.common.thread.MonitoringThreadPool;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程工具类
 *
 * <AUTHOR>
 * @since 2022/4/26 14:27
 */
public class ThreadUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(ThreadUtil.class);
    public static ExecutorService EXECUTOR_SERVICE = null;
    public static ExecutorService LOG_EXECUTOR_SERVICE = null;

    /**
     * 增加了JVM关闭钩子，确保在应用退出时能尝试优雅地关闭线程池，避免资源泄露
     */
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(ThreadUtil::shutdownThreadPoolGracefully));
    }

    public static synchronized void init(int corePoolSize, int maximumPoolSize, long keepAliveTime, int queueSize, boolean printLog) {
        if (Objects.nonNull(EXECUTOR_SERVICE)) {
            LOGGER.warn("EXECUTOR_SERVICE 线程池已初始化，重复初始化请求被忽略。");
            return;
        }
        if (printLog) {
            EXECUTOR_SERVICE = TtlExecutors.getTtlExecutorService(
                    MoreExecutors.listeningDecorator(new MonitoringThreadPool(
                            corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(queueSize),
                            new ThreadFactoryBuilder().setDaemon(true)
                                    .setNameFormat("ThreadUtil-%d")
                                    .setUncaughtExceptionHandler((t, e) -> {
                                        // StringWriter sw = new StringWriter();
                                        // e.printStackTrace(new PrintWriter(sw));
                                        LOGGER.error("线程池任务处理异常!", e);
                                    })
                                    .setThreadFactory(Thread::new).build(),
                            // 设置拒绝策略为CallerRunsPolicy
                            new LoggingCallerRunsPolicy()
                    )));
        } else {
            EXECUTOR_SERVICE = TtlExecutors.getTtlExecutorService(
                    MoreExecutors.listeningDecorator(new ThreadPoolExecutor(
                            corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(queueSize),
                            new ThreadFactoryBuilder().setDaemon(true)
                                    .setNameFormat("ThreadUtil-%d")
                                    .setUncaughtExceptionHandler((t, e) -> {
                                        // StringWriter sw = new StringWriter();
                                        // e.printStackTrace(new PrintWriter(sw));
                                        LOGGER.error("线程池任务处理异常!", e);
                                    })
                                    .setThreadFactory(Thread::new).build(),
                            // 设置拒绝策略为CallerRunsPolicy
                            new LoggingCallerRunsPolicy()
                    )));
        }

        LOGGER.info("EXECUTOR_SERVICE 线程池初始化成功: 核心线程数: {}, 最大线程数: {}, 空闲存活时间: {} 秒, 队列大小: {}",
                corePoolSize, maximumPoolSize, keepAliveTime, queueSize);
    }

    public static synchronized void initLog(int corePoolSize, int maximumPoolSize, long keepAliveTime) {
        if (Objects.nonNull(LOG_EXECUTOR_SERVICE)) {
            LOGGER.warn("LOG_EXECUTOR_SERVICE 线程池已初始化，重复初始化请求被忽略。");
            return;
        }

        // 日志专用
        LOG_EXECUTOR_SERVICE = TtlExecutors.getTtlExecutorService(
                MoreExecutors.listeningDecorator(new ThreadPoolExecutor(
                        corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS,
                        new LinkedBlockingQueue<>(),
                        new ThreadFactoryBuilder().setDaemon(true)
                                .setNameFormat("LOG-EXECUTOR-SERVICE-%d")
                                .setUncaughtExceptionHandler((t, e) -> LOGGER.error("日志任务处理异常!", e))
                                .setThreadFactory(Thread::new).build(),
                        new LoggingCallerRunsPolicy() // 饱和策略：由调用线程执行任务))
                )));
        LOGGER.info("LOG_EXECUTOR_SERVICE 线程池初始化成功: 核心线程数: {}, 最大线程数: {}, 空闲存活时间: {} 秒}",
                corePoolSize, maximumPoolSize, keepAliveTime);
    }

    /**
     * 获取线程池实例
     * 如果未初始化，则抛出异常。
     *
     * @return ExecutorService
     */
    public static ExecutorService getExecutorService() {
        if (EXECUTOR_SERVICE == null) {
            throw new IllegalStateException("EXECUTOR_SERVICE 线程池未初始化，请先调用 init 方法初始化线程池。");
        }
        return EXECUTOR_SERVICE;
    }

    public static ExecutorService getLogExecutorService() {
        if (LOG_EXECUTOR_SERVICE == null) {
            throw new IllegalStateException("LOG_EXECUTOR_SERVICE 线程池未初始化，请先调用 initLog 方法初始化线程池。");
        }
        return LOG_EXECUTOR_SERVICE;
    }

    private static void shutdownThreadPoolGracefully() {
        if (Objects.nonNull(EXECUTOR_SERVICE)) {
            LOGGER.warn("关闭线程池 EXECUTOR_SERVICE");
            EXECUTOR_SERVICE.shutdown();
            try {
                if (!EXECUTOR_SERVICE.awaitTermination(60, TimeUnit.SECONDS)) {
                    EXECUTOR_SERVICE.shutdownNow();
                    if (!EXECUTOR_SERVICE.awaitTermination(60, TimeUnit.SECONDS))
                        LOGGER.warn("线程池未优雅关闭");
                }
            } catch (InterruptedException ie) {
                EXECUTOR_SERVICE.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        if (Objects.nonNull(LOG_EXECUTOR_SERVICE)) {
            LOGGER.warn("关闭线程池 LOG_EXECUTOR_SERVICE");
            LOG_EXECUTOR_SERVICE.shutdown();
            try {
                if (!LOG_EXECUTOR_SERVICE.awaitTermination(60, TimeUnit.SECONDS)) {
                    LOG_EXECUTOR_SERVICE.shutdownNow();
                    if (!LOG_EXECUTOR_SERVICE.awaitTermination(60, TimeUnit.SECONDS))
                        LOGGER.warn("线程池未优雅关闭");
                }
            } catch (InterruptedException ie) {
                LOG_EXECUTOR_SERVICE.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    // public static void main(String[] args) throws InterruptedException {
    //     ThreadUtil.init(12,64,60,100);
    //     submitTestTasks();
    //     Thread.sleep(60000);
    //     // 保持程序运行以便监控线程池状态
    //     System.out.println("程序执行完毕");
    // }
    //
    // public static void submitTestTasks() {
    //     for (int i = 0; i < 100; i++) {
    //         int finalI = i;
    //         EXECUTOR_SERVICE.execute(() -> {
    //             try {
    //                 System.out.println(finalI);
    //                 Thread.sleep(2000); // 模拟任务执行 2 秒
    //             } catch (InterruptedException e) {
    //                 Thread.currentThread().interrupt();
    //             }
    //         });
    //     }
    // }
}
