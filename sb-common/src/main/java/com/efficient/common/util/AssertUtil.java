package com.efficient.common.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.efficient.common.exception.CommonException;
import com.efficient.common.result.ResultEnum;

import java.util.Objects;

/**
 * 断言工具类
 *
 * <AUTHOR>
 * @since 2024/12/12 10:16
 */
public class AssertUtil {

    public static void notNull(Object data) {
        if (Objects.isNull(data)) {
            throw new CommonException(ResultEnum.DATA_NOT_EXIST);
        }
    }

    public static void notNull(Object data, String errorMessage) {
        if (Objects.isNull(data)) {
            throw new CommonException(ResultEnum.COMMON_ERROR.getCode(), errorMessage);
        }
    }

    public static void notBlank(String data) {
        if (StrUtil.isBlank(data)) {
            throw new CommonException(ResultEnum.DATA_NOT_EXIST);
        }
    }

    public static void notBlank(String data, String errorMessage) {
        if (StrUtil.isBlank(data)) {
            throw new CommonException(ResultEnum.COMMON_ERROR.getCode(), errorMessage);
        }
    }

    public static void equals(Object data1, Object data2) {
        if (!ObjectUtil.equals(data1, data2)) {
            throw new CommonException(ResultEnum.DATA_NOT_EQUALS);
        }
    }

    public static void equals(Object data1, Object data2, String errorMessage) {
        if (!ObjectUtil.equals(data1, data2)) {
            throw new CommonException(ResultEnum.COMMON_ERROR.getCode(), errorMessage);
        }
    }

}
