package com.efficient.common.api;

/**
 * 密码服务接口
 * 用于解耦用户管理模块与认证模块的密码处理逻辑
 * 
 * <AUTHOR>
 * @since 2024/7/28
 */
public interface PasswordService {
    
    /**
     * 密码加密
     * 用于用户注册、导入等场景的密码加密
     * 
     * @param password 原始密码
     * @return 加密后的密码
     */
    String encrypt(String password);
    
    /**
     * 创建密码
     * 用于密码重置等场景的密码创建
     * 
     * @param password 原始密码
     * @return 加密后的密码
     */
    String createPassword(String password);
    
    /**
     * 验证密码
     * 用于密码校验场景
     * 
     * @param cryptPassword 加密后的密码
     * @param password 原始密码
     * @return 是否匹配
     */
    boolean checkEncrypt(String cryptPassword, String password);
}
