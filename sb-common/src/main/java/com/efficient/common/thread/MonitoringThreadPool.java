package com.efficient.common.thread;

import com.alibaba.ttl.TransmittableThreadLocal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;

/**
 * 线程池监控
 *
 * <AUTHOR>
 * @since 2024/12/6 11:03
 */
public class MonitoringThreadPool extends ThreadPoolExecutor {
    private static final Logger LOGGER = LoggerFactory.getLogger(MonitoringThreadPool.class);
    private TransmittableThreadLocal<Long> startTimeThreadLocal = new TransmittableThreadLocal<>();
    private TransmittableThreadLocal<String> mainThreadCaller = new TransmittableThreadLocal<>();

    public MonitoringThreadPool(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
                                BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }

    @Override
    public void execute(Runnable command) {
        // 在任务提交时记录主线程调用类信息
        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            StackTraceElement stackTraceElement = stackTrace[4];
            String callerClass = stackTraceElement.getClassName() + "." + stackTraceElement.getMethodName();
            // 包装任务，携带调用类信息
            command = new ContextAwareRunnable(command, callerClass);
        } finally {
            // 包装 Runnable 任务，传递主线程信息
            super.execute(command); // 包装任务
        }
    }

    /**
     * 任务执行之前，记录任务开始时间
     */
    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        startTimeThreadLocal.set(System.currentTimeMillis());
        // 提取任务中的调用类信息，并设置到 ThreadLocal
        if (r instanceof ContextAwareRunnable) {
            String callerClass = ((ContextAwareRunnable) r).getCallerClass();
            mainThreadCaller.set(callerClass);
        }
    }

    /**
     * 任务执行之后，计算任务结束时间
     */
    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        try {
            long costTime = System.currentTimeMillis() - startTimeThreadLocal.get();
            String callerClass = mainThreadCaller.get();
            LOGGER.info("线程池状态: 调用线程的主类: {},任务耗时: {} ms, 核心线程数: {}, 最大允许的线程数: {}, 执行的任务数量: {}, 已完成任务数量: {}, 任务总数: {}, 队列里缓存的任务数量: {}",
                    callerClass,
                    costTime, this.getCorePoolSize(), this.getMaximumPoolSize(), this.getActiveCount(),
                    this.getCompletedTaskCount(), this.getTaskCount(), this.getQueue().size());
        } finally {
            startTimeThreadLocal.remove();
            mainThreadCaller.remove();
        }
    }

    private static class ContextAwareRunnable implements Runnable {
        private final Runnable task;
        private final String callerClass;

        public ContextAwareRunnable(Runnable task, String callerClass) {
            this.task = task;
            this.callerClass = callerClass;
        }

        public String getCallerClass() {
            return callerClass;
        }

        @Override
        public void run() {
            task.run();
        }
    }

}
