package com.efficient.common.thread;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @since 2024/12/6 14:05
 */
@Slf4j
public class LoggingCallerRunsPolicy extends ThreadPoolExecutor.CallerRunsPolicy {

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
        if (!e.isShutdown()) {
            StackTraceElement stackTraceElement = Thread.currentThread().getStackTrace()[7];
            String threadName = stackTraceElement.getClassName() + "." + stackTraceElement.getMethodName();
            log.warn("线程池已满，任务被拒绝,改由调用线程执行:{},线程池状态： 核心线程数: {}, 最大允许的线程数: {}, 执行的任务数量: {}, 已完成任务数量: {}, 任务总数: {}, 队列里缓存的任务数量: {}",
                    threadName,
                    e.getCorePoolSize(), e.getMaximumPoolSize(), e.getActiveCount(),
                    e.getCompletedTaskCount(), e.getTaskCount(), e.getQueue().size());
            super.rejectedExecution(r, e);
        }
    }
}
