package com.efficient.common.exception;

import com.efficient.common.result.ResultEnum;
import lombok.Data;

/**
 * 通用异常
 *
 * <AUTHOR>
 * @since 2024/12/12 10:06
 */
@Data
public class CommonException extends RuntimeException {

    private Integer code;
    private String msg;
    private Object data;

    public CommonException() {
    }

    public CommonException(ResultEnum result, Throwable cause) {
        super(cause);
        this.code = result.getCode();
        this.msg = result.getMsg();
    }

    public CommonException(ResultEnum result) {
        this.code = result.getCode();
        this.msg = result.getMsg();
    }

    public CommonException(String msg) {
        this.code = ResultEnum.COMMON_ERROR.getCode();
        this.msg = msg;
    }

    public CommonException(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public CommonException(Integer code, String msg, Object data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }
}
