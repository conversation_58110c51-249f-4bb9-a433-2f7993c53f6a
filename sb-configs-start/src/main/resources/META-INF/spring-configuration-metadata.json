{"groups": [{"name": "com.efficient.configs", "type": "com.efficient.configs.properties.ConfigsProperties", "sourceType": "com.efficient.configs.config.ConfigsConfig"}], "properties": [{"name": "com.efficient.configs.cors.enable", "type": "java.lang.Bo<PERSON>an", "description": "是否启用跨越配置", "defaultValue": true, "sourceType": "com.efficient.configs.properties.CorsProperties"}, {"name": "com.efficient.configs.webSocket.enable", "type": "java.lang.Bo<PERSON>an", "description": "是否启用WebSocket配置", "defaultValue": false, "sourceType": "com.efficient.configs.properties.WebSocketProperties"}, {"name": "com.efficient.configs.enableMybatisPlusFill", "type": "java.lang.Bo<PERSON>an", "description": "是否启用 mybatis-plus 自动注入功能", "defaultValue": true, "sourceType": "com.efficient.configs.properties.ConfigsProperties"}, {"name": "com.efficient.configs.threadPool.core.corePoolSize", "type": "java.lang.Integer", "description": "核心线程数", "defaultValue": 12, "sourceType": "com.efficient.configs.properties.ThreadPoolProperties"}, {"name": "com.efficient.configs.threadPool.core.maximumPoolSize", "type": "java.lang.Integer", "description": "最大线程数", "defaultValue": 64, "sourceType": "com.efficient.configs.properties.ThreadPoolProperties"}, {"name": "com.efficient.configs.threadPool.core.keepAliveTime", "type": "java.lang.Long", "description": "存活时间，单位秒", "defaultValue": 60, "sourceType": "com.efficient.configs.properties.ThreadPoolProperties"}, {"name": "com.efficient.configs.threadPool.core.queueSize", "type": "java.lang.Integer", "description": "线程池缓存长度", "defaultValue": "Integer.MAX_VALUE", "sourceType": "com.efficient.configs.properties.ThreadPoolProperties"}, {"name": "com.efficient.configs.threadPool.core.printLog", "type": "java.lang.Bo<PERSON>an", "description": "是否输出线程池监控日志", "defaultValue": "true", "sourceType": "com.efficient.configs.properties.ThreadPoolProperties"}, {"name": "com.efficient.configs.threadPool.log.corePoolSize", "type": "java.lang.Integer", "description": "核心线程数", "defaultValue": 12, "sourceType": "com.efficient.configs.properties.ThreadPoolProperties"}, {"name": "com.efficient.configs.threadPool.log.maximumPoolSize", "type": "java.lang.Integer", "description": "最大线程数", "defaultValue": 64, "sourceType": "com.efficient.configs.properties.ThreadPoolProperties"}, {"name": "com.efficient.configs.threadPool.log.keepAliveTime", "type": "java.lang.Long", "description": "存活时间，单位秒", "defaultValue": 60, "sourceType": "com.efficient.configs.properties.ThreadPoolProperties"}], "hints": []}