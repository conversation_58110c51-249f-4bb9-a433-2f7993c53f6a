package com.efficient.configs.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2022/10/17 15:56
 */
@ConfigurationProperties("com.efficient.configs")
@Data
public class ConfigsProperties {
    private CorsProperties cors = new CorsProperties();
    private WebSocketProperties webSocket = new WebSocketProperties();
    private ThreadPoolProperties threadPool = new ThreadPoolProperties();
    /**
     * 是否启用 mybatis-plus 自动注入功能
     */
    private Boolean enableMybatisPlusFill = true;
}
