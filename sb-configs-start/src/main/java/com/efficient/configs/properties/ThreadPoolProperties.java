package com.efficient.configs.properties;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/9 17:12
 */
@Data
public class ThreadPoolProperties {
    /**
     * 核心业务线程池
     */
    private CorePoolProperties core = new CorePoolProperties();
    /**
     * 日志线程池
     */
    private LogPoolProperties log = new LogPoolProperties();

    @Data
    public static class CorePoolProperties {
        /**
         * 核心线程数
         */
        private int corePoolSize = 12;
        /**
         * 最大线程数
         */
        private int maximumPoolSize = 64;
        /**
         * 存活时间，单位秒
         */
        private long keepAliveTime = 60;
        /**
         * 核心线程数
         */
        private int queueSize = Integer.MAX_VALUE;
        /**
         * 是否输出日志
         */
        private boolean printLog = true;
    }

    @Data
    public static class LogPoolProperties {
        /**
         * 缓存线程数
         */
        private int corePoolSize = 1;
        /**
         * 最大线程数
         */
        private int maximumPoolSize = 1;
        /**
         * 存活时间，单位秒
         */
        private long keepAliveTime = 60;
    }
}
