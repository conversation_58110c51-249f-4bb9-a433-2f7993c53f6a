package com.efficient.configs.service;

import com.efficient.common.api.StartedEventServer;
import com.efficient.common.util.ThreadUtil;
import com.efficient.configs.properties.ConfigsProperties;
import com.efficient.configs.properties.ThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/12/9 17:32
 */
@Slf4j
@Service
public class ConfigStartedEvent implements StartedEventServer {
    @Autowired
    private ConfigsProperties configsProperties;

    @Override
    public void init() {
        ThreadPoolProperties threadPool = configsProperties.getThreadPool();
        ThreadPoolProperties.CorePoolProperties coreProperties = threadPool.getCore();
        ThreadPoolProperties.LogPoolProperties logProperties = threadPool.getLog();
        ThreadUtil.init(coreProperties.getCorePoolSize(), coreProperties.getMaximumPoolSize(), coreProperties.getKeepAliveTime(), coreProperties.getQueueSize(), coreProperties.isPrintLog());
        ThreadUtil.initLog(logProperties.getCorePoolSize(), logProperties.getMaximumPoolSize(), logProperties.getKeepAliveTime());
        log.info("初始化线程池成功!");
    }
}
