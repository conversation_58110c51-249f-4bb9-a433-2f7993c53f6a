package com.efficient.configs.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.efficient.common.auth.RequestHolder;
import com.efficient.common.auth.UserTicket;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/18 17:00
 */
@ConditionalOnProperty(name = "com.efficient.configs.enableMybatisPlusFill", matchIfMissing = true, havingValue = "true")
@Component
@Slf4j
public class PlusMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
        UserTicket currUser = RequestHolder.getCurrUser();
        if (Objects.nonNull(currUser)) {
            this.strictInsertFill(metaObject, "createUser", String.class, currUser.getUserId());
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
        UserTicket currUser = RequestHolder.getCurrUser();
        if (Objects.nonNull(currUser)) {
            this.strictUpdateFill(metaObject, "updateUser", String.class, currUser.getUserId());
        }
    }
}
