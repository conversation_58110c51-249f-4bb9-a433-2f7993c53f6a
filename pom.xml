<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <name>sb-parent</name>
    <description>基于springboot的二次封装框架</description>
    <modules>
        <module>sb-common</module>
        <module>sb-swagger-start</module>
        <module>sb-file-start</module>
        <module>sb-task-start</module>
        <module>sb-cache-start</module>
        <module>sb-logs-start</module>
        <module>sb-configs-start</module>
        <module>sb-auth-start</module>
        <module>sb-rate-start</module>
        <module>sb-data-security-start</module>
        <module>sb-yu-start</module>
        <module>sb-system-start</module>
        <module>sb-openapi-start</module>
        <module>sb-elasticsearch-start</module>
        <module>sb-form-start</module>
        <module>sb-irs-start</module>
        <module>sb-uc-start</module>
        <module>sb-camera-start</module>
    </modules>
    <groupId>com.zenith</groupId>
    <artifactId>sb</artifactId>
    <version>2.2.1-20250728-02-beta</version>
    <packaging>pom</packaging>

    <properties>
        <sb.version>2.2.1-20250728-02-beta</sb.version>
        <java.version>1.8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <resource.delimiter>@</resource.delimiter>

        <spring-boot.version>2.7.18</spring-boot.version>
        <mysql.version>8.3.0</mysql.version>
        <pgsql.version>42.7.3</pgsql.version>
        <oracle.version>23.2.0.0</oracle.version>
        <dm.version>7.18</dm.version>
        <kingbase.version>8.2.0</kingbase.version>

        <aspose-cells.version>21.11</aspose-cells.version>
        <aspose-words.version>21.6</aspose-words.version>
        <poi.version>5.2.5</poi.version>

        <lombok.version>1.18.32</lombok.version>
        <hutool.version>5.8.27</hutool.version>
        <guava.version>33.1.0-jre</guava.version>
        <transmittable.version>2.14.5</transmittable.version>
        <jooq.version>3.14.16</jooq.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <mybatis-plus.version>3.5.6</mybatis-plus.version>
        <swagger.version>3.0.0</swagger.version>

        <analysis-core.version>1.0.0</analysis-core.version>
        <dbsync.version>1.0.0</dbsync.version>
        <java-jwt.version>4.4.0</java-jwt.version>
        <hanlp.version>portable-1.8.4</hanlp.version>
        <minio.version>8.5.7</minio.version>
        <reflections.version>0.10.2</reflections.version>

        <bcprov-jdk15on.version>1.70</bcprov-jdk15on.version>
        <bcpkix-jdk15on.version>1.70</bcpkix-jdk15on.version>
        <joda-time.version>2.12.6</joda-time.version>
        <fastjson.version>2.0.49</fastjson.version>
        <redisson.version>3.29.0</redisson.version>
        <mvel2.version>2.5.2.Final</mvel2.version>
        <commons.version>1.16.1</commons.version>
        <googleauth.version>1.5.0</googleauth.version>
        <easyexcel.version>3.3.4</easyexcel.version>
        <easypoi.version>4.5.0</easypoi.version>
        <elasticsearch.version>7.17.21</elasticsearch.version>
        <druid.version>1.2.22</druid.version>
        <flink.version>1.19.0</flink.version>
        <flink-kafka.version>3.1.0-1.18</flink-kafka.version>
        <spring-kafka.version>2.9.13</spring-kafka.version>
    </properties>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- swagger -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <!-- jdbc -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-core</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.version}</version>
            </dependency>
            <dependency>
                <groupId>dm</groupId>
                <artifactId>dm7</artifactId>
                <version>${dm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.kingbase8</groupId>
                <artifactId>jdbc42</artifactId>
                <version>${oracle.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${pgsql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>jooq</artifactId>
                <version>${jooq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>org.apache.poi</groupId>-->
            <!--                <artifactId>poi-ooxml</artifactId>-->
            <!--                <version>${poi.version}</version>-->
            <!--            </dependency>-->
            <!--            <dependency>-->
            <!--                <groupId>org.apache.poi</groupId>-->
            <!--                <artifactId>poi</artifactId>-->
            <!--                <version>${poi.version}</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aspose</groupId>
                <artifactId>aspose-cells</artifactId>
                <version>${aspose-cells.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aspose</groupId>
                <artifactId>aspose-words</artifactId>
                <version>${aspose-words.version}</version>
            </dependency>

            <!-- 自定义模块 -->
            <dependency>
                <groupId>com.sjr</groupId>
                <artifactId>analysis-core</artifactId>
                <version>${analysis-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sjr</groupId>
                <artifactId>dbsync</artifactId>
                <version>${dbsync.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-common</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-swagger-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-file-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-task-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-cache-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-logs-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-configs-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-auth-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-rate-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-data-security-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-yu-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-system-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-uc-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-openapi-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-elasticsearch-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-form-start</artifactId>
                <version>${sb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>sb-camera-start</artifactId>
                <version>${sb.version}</version>
            </dependency>

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java-jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hankcs</groupId>
                <artifactId>hanlp</artifactId>
                <version>${hanlp.version}</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bcprov-jdk15on.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
                <version>${bcpkix-jdk15on.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>org.apache.httpcomponents.client5</groupId>-->
            <!--                <artifactId>httpclient5</artifactId>-->
            <!--                <version>${httpclient.version}</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mvel</groupId>
                <artifactId>mvel2</artifactId>
                <version>${mvel2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.warrenstrange</groupId>
                <artifactId>googleauth</artifactId>
                <version>${googleauth.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>co.elastic.clients</groupId>
                <artifactId>elasticsearch-java</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-java</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-streaming-java</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-clients</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-files</artifactId>
                <version>${flink.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-connector-kafka</artifactId>
                <version>${flink-kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-kafka.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>disable-javadoc-doclint</id>
            <activation>
                <jdk>[1.8,)</jdk>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <javadoc.opts>-Xdoclint:none</javadoc.opts>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.8.1</version>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>nexus</id>
            <url>http://192.168.0.76:9999/repository/tanmw-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus</id>
            <url>http://192.168.0.76:9999/repository/tanmw-releases/</url>
        </snapshotRepository>
    </distributionManagement>

</project>