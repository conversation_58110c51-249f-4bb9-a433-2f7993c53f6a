# SB框架 - 模块功能说明文档

## 项目概述
SB框架是基于Spring Boot的二次封装框架，提供了多种企业级应用开发常用的功能模块，支持模块化开发和灵活配置。

## Maven配置

### 中央仓库配置
```xml
<repositories>
    <repository>
        <id>central</id>
        <url>http://************:9999/repository/maven-public/</url>
        <releases>
            <enabled>true</enabled>
        </releases>
        <snapshots>
            <enabled>true</enabled>
        </snapshots>
    </repository>
</repositories>
```

### Maven设置文件
请参考项目中的`settings-76.xml`配置文件。

## 依赖安装

### Aspose相关依赖
```
mvn -Dmaven.test.skip=true install

mvn install:install-file -DgroupId=com.aspose -DartifactId=aspose-cells -Dversion=21.11 -Dpackaging=jar -Dfile=./aspose-cells-21.11.jar

mvn install:install-file -DgroupId=com.aspose -DartifactId=aspose-words -Dversion=21.6 -Dpackaging=jar -Dfile=./aspose-words-21.11.0-jdk17.jar

mvn install:install-file -DgroupId=com.hkws -DartifactId=artemis-http-client -Dversion=1.1.13.RELEASE -Dpackaging=jar -Dfile=./artemis-http-client-1.1.13.RELEASE.jar
```

### YKZ相关依赖
```
mvn -Dmaven.test.skip=true install
mvn install:install-file -DgroupId=com.dcqc -DartifactId=dcqc-uc-oauth-sdk -Dversion=3.0.0-RELEASE -Dpackaging=jar -Dfile=./dcqc-uc-oauth-sdk-3.0.0-RELEASE.jar

mvn install:install-file -DgroupId=com.alibaba -DartifactId=zwdd-sdk-java -Dversion=1.2.0 -Dpackaging=jar -Dfile=./zwdd-sdk-java-1.2.0.jar
```

## 版本管理
```
mvn versions:set -DnewVersion=新版本号
```

## 模块说明

### 1. 核心基础模块

#### 1.1 sb-common
**描述**：公共基础模块，包含通用工具类、基础实体类、常量定义等。
**功能**：
- 提供通用工具类
- 定义基础实体类
- 提供线程池管理
- 统一异常处理
- 提供常量定义

#### 1.2 sb-configs-start
**描述**：配置管理模块，负责系统配置的加载、缓存和管理。
**配置参数**：
```yaml
com:
  efficient:
    configs:
      # 线程池配置
      thread-pool:
        # 核心业务线程池
        core:
          core-pool-size: 12
          maximum-pool-size: 64
          keep-alive-time: 60
          queue-size: 2147483647
          print-log: true
        # 日志线程池
        log:
          core-pool-size: 1
          maximum-pool-size: 1
          keep-alive-time: 60
```
**功能**：
- 统一配置加载与管理
- 线程池配置
- 跨域配置

#### 1.3 sb-auth-start
**描述**：认证与授权模块，处理用户认证、权限校验、登录等功能。
**配置参数**：
```yaml
com:
  efficient:
    auth:
      # 系统认证方式，默认系统自带认证
      auth-service: "default"
      # 系统ID字段
      system-id-field: "systemId"
      # 权限认证方式
      permission-check-type: "default"
      # 用户类名称
      user-ticket-class-name: "com.efficient.common.auth.UserTicket"
      # 白名单列表
      white-list:
        - "**swagger-resources**"
        - "/login"
        - "/captcha"
      # 登录相关配置
      login:
        # 登录模式 
        mode: "default"
        # 验证码开启
        captcha-enable: true
        # 验证码类型
        captcha-type: "default"
```
**功能**：
- 用户认证
- 权限校验
- 登录验证
- 白名单管理
- Token管理

#### 1.4 sb-swagger-start
**描述**：API文档生成模块，集成Swagger为API生成在线文档。
**配置参数**：
```yaml
com:
  efficient:
    swagger:
      # 是否启用
      enable: true
      # 标题
      title: "Efficient Boot"
      # 描述
      description: "Efficient Boot"
      # 服务条款网址
      terms-of-service-url: "https://www.baidu.com"
      # 版本号
      version: "1.0.0"
```
**功能**：
- API文档生成
- 在线API测试
- 接口说明管理

### 2. 文件处理模块

#### 2.1 sb-file-start
**描述**：文件服务模块，提供文件上传、下载、分片上传等功能。
**配置参数**：
```yaml
com:
  efficient:
    file:
      # 存储方式：local、minio等
      active: "local"
      # 临时文件路径
      temp-path: "/home/<USER>/file/temp/"
      # 本地存储配置
      local:
        # 本地存储路径
        local-path: "/home/<USER>/file/"
        # 是否添加日期前缀
        add-date-prefix: true
        # 是否文件重命名
        rename: false
```
**功能**：
- 文件上传与下载
- 大文件分片上传
- 文件秒传(MD5校验)
- 支持本地存储
- 支持对接Minio等对象存储

### 3. 日志和监控模块

#### 3.1 sb-logs-start
**描述**：日志管理模块，提供操作日志记录、SQL日志记录等功能。
**配置参数**：
```yaml
com:
  efficient:
    logs:
      # 是否存储数据库
      db: true
      # 日志等级
      level: "info"
      # 日志名称
      name: "log"
      # 系统ID字段
      system-id-field: "systemId"
      # 日志存储路径
      path: "/efficient/logs/"
      # SQL日志配置
      sql:
        # 是否开启
        enable: true
        # 是否输出参数
        parameter: true
        # 是否格式化
        format: false
```
**功能**：
- 操作日志记录
- SQL日志记录
- 自定义日志函数
- 日志异步处理

### 4. 缓存和任务模块

#### 4.1 sb-cache-start
**描述**：缓存管理模块，集成Redis、本地缓存等多种缓存方案。
**配置参数**：
```yaml
com:
  efficient:
    cache:
      # 缓存类型：local、redis
      type: "redis"
      # 缓存前缀
      prefix: "efficient:"
      # 本地缓存配置
      local:
        # 最大缓存数量
        max-size: 1000
        # 过期时间(秒)
        expire: 3600
```
**功能**：
- Redis缓存
- 本地缓存
- 分布式锁
- 缓存管理

#### 4.2 sb-task-start
**描述**：任务调度模块，提供定时任务、异步任务等功能。
**配置参数**：
```yaml
com:
  efficient:
    task:
      # 是否启用
      enable: true
      # 线程池大小
      pool-size: 10
      # 任务前缀
      task-prefix: "task_"
```
**功能**：
- 定时任务调度
- 异步任务处理
- 并行任务执行
- 任务状态监控

### 5. 业务功能模块

#### 5.1 sb-system-start
**描述**：系统管理模块，提供用户、角色、菜单、部门等基础管理功能。
**功能**：
- 用户管理
- 角色管理
- 菜单管理
- 部门管理
- 权限分配

#### 5.2 sb-yu-start
**描述**：用户中心模块，集成第三方用户中心，支持组织架构管理。
**配置参数**：
```yaml
com:
  efficient:
    ykz:
      # 用户中心配置
      user-center:
        # 用户中心地址
        url: "http://xxx.xxx.xxx.xxx"
        # 应用ID
        app-id: "your-app-id"
        # 应用密钥
        app-secret: "your-app-secret"
        # 租户ID
        tenant-id: "your-tenant-id"
      # API配置
      api:
        # 用户接口
        user: "/api/user"
        # 部门接口
        dept: "/api/dept"
        # 岗位接口
        post: "/api/post"
```
**功能**：
- 用户信息同步
- 组织架构管理
- 用户认证集成
- 岗位管理

#### 5.3 sb-openapi-start
**描述**：开放API模块，提供对外开放的API接口。
**配置参数**：
```yaml
com:
  efficient:
    openapi:
      # 是否启用
      enable: true
      # API前缀
      prefix: "/api"
```
**功能**：
- API签名验证
- 接口权限控制
- 第三方对接

#### 5.4 sb-elasticsearch-start
**描述**：搜索引擎模块，集成Elasticsearch提供全文检索功能。
**功能**：
- 文档索引
- 全文检索
- 聚合分析
- 高亮显示

#### 5.5 sb-form-start
**描述**：表单处理模块，提供动态表单、表单验证等功能。
**配置参数**：
```yaml
com:
  efficient:
    form:
      # 表单存储路径
      path: "/efficient/form/"
```
**功能**：
- 动态表单配置
- 表单验证
- 表单数据处理
- 表单模板管理

#### 5.6 sb-irs-start
**描述**：集成服务模块，提供与第三方系统的集成接口。
**配置参数**：
```yaml
com:
  efficient:
    irs:
      # 服务地址
      url: "http://xxx.xxx.xxx.xxx"
      # 应用ID
      app-id: "your-app-id"
      # 应用密钥
      app-secret: "your-app-secret"
```
**功能**：
- 第三方系统集成
- 数据同步
- 接口调用

#### 5.7 sb-uc-start
**描述**：用户中心模块，提供用户管理、认证等功能。
**功能**：
- 用户管理
- 用户认证
- 用户信息同步

#### 5.8 sb-camera-start
**描述**：摄像头服务模块，提供摄像头接入、视频流处理等功能。
**功能**：
- 摄像头接入
- 视频流处理
- 视频录制存储

### 6. 安全相关模块

#### 6.1 sb-data-security-start
**描述**：数据安全模块，提供数据加密、脱敏等功能。
**功能**：
- 数据加密解密
- 数据脱敏
- 敏感信息处理

#### 6.2 sb-rate-start
**描述**：限流控制模块，提供接口限流、访问控制等功能。
**配置参数**：
```yaml
com:
  efficient:
    rate:
      # 是否启用
      enable: true
      # 限流类型：ip、user、api
      type: "ip"
      # 时间窗口(秒)
      window: 60
      # 最大请求数
      max: 100
      # 限流策略
      strategy: "token_bucket"
```
**功能**：
- 接口限流
- IP限流
- 用户限流
- 限流策略配置

## 技术栈

- 核心框架：Spring Boot 2.7.x
- ORM框架：MyBatis-Plus 3.5.x
- 数据库支持：MySQL、PostgreSQL、Oracle、达梦、人大金仓
- 缓存：Redis、Redisson
- 文档：Swagger 3.0
- 工具库：Hutool、Guava
- Excel处理：EasyExcel、EasyPoi
- 文档处理：Aspose-Words、Aspose-Cells
- 搜索引擎：Elasticsearch 7.x
