<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zenith</groupId>
        <artifactId>sb</artifactId>
        <version>2.2.1-20250728-02-beta</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <description>文件存储 start</description>
    <artifactId>sb-file-start</artifactId>

    <properties>
        <!--        <maven.deploy.skip>true</maven.deploy.skip>-->
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-common</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-swagger-start</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-logs-start</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>
    <!--    <build>-->
    <!--        <plugins>-->
    <!--            <plugin>-->
    <!--                <groupId>org.apache.maven.plugins</groupId>-->
    <!--                <artifactId>maven-deploy-plugin</artifactId>-->
    <!--                <configuration>-->
    <!--                    <skip>true</skip>-->
    <!--                </configuration>-->
    <!--            </plugin>-->
    <!--            <plugin>-->
    <!--                <groupId>org.sonatype.plugins</groupId>-->
    <!--                <artifactId>nexus-staging-maven-plugin</artifactId>-->
    <!--                <configuration>-->
    <!--                    <skipNexusStagingDeployMojo>true</skipNexusStagingDeployMojo>-->
    <!--                </configuration>-->
    <!--            </plugin>-->
    <!--        </plugins>-->
    <!--    </build>-->
</project>