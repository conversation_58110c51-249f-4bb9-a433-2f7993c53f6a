{"groups": [{"name": "com.efficient.file", "type": "com.efficient.file.properties.FileProperties", "sourceType": "com.efficient.file.config.FileConfig"}], "properties": [{"name": "com.efficient.file.active", "type": "com.efficient.file.constant.StoreEnum", "description": "文件存储方式", "defaultValue": "local", "sourceType": "com.efficient.file.properties.FileProperties"}, {"name": "com.efficient.file.tempPath", "type": "java.lang.String", "description": "临时文件存储路径", "defaultValue": "/efficient/file/temp/", "sourceType": "com.efficient.file.properties.FileProperties"}, {"name": "com.efficient.file.local.localPath", "type": "java.lang.String", "description": "文件存储路径", "defaultValue": "/efficient/file/", "sourceType": "com.efficient.file.properties.FileProperties.Local"}, {"name": "com.efficient.file.local.addDatePrefix", "type": "java.lang.Bo<PERSON>an", "description": "文件存储路径是否添加时间文件,eg:/2022/01/01/demo.sql", "defaultValue": true, "sourceType": "com.efficient.file.properties.FileProperties.Local"}, {"name": "com.efficient.file.local.rename", "type": "java.lang.Bo<PERSON>an", "description": "是否对文件重命名", "defaultValue": false, "sourceType": "com.efficient.file.properties.FileProperties.Local"}], "hints": [{"name": "com.efficient.file.active", "values": [{"value": "local", "description": "本地存储"}, {"value": "db", "description": "数据库存储"}, {"value": "minio", "description": "minio"}]}]}