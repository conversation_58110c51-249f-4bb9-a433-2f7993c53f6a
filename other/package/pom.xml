<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <!--仓库-->
    <repositories>
        <repository>
            <id>sonatype</id>
            <name>sonatype maven</name>
            <url>https://repo.maven.apache.org/maven2/</url>
            <!--            <url>https://maven.aliyun.com/repository/central/</url>-->
            <layout>default</layout>
        </repository>
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>
    </repositories>


    <!-- 编译打包 -->
    <build>
        <finalName>sb-${project.version}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>templates/**</exclude>
                    <exclude>**/*.xls</exclude>
                    <exclude>**/*.xlsx</exclude>
                    <exclude>**/*.xlsm</exclude>
                    <exclude>**/*.doc</exclude>
                    <exclude>**/*.docx</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>templates/**</include>
                    <include>**/*.xls</include>
                    <include>**/*.xlsx</include>
                    <include>**/*.xlsm</include>
                    <include>**/*.doc</include>
                    <include>**/*.docx</include>
                </includes>
            </resource>
        </resources>


        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>cer</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <!-- 将静态资源排除出 jar 包 -->
                    <excludes>
                        <exclude>*.**</exclude>
                        <exclude>static/**</exclude>
                        <exclude>public/**</exclude>
                        <exclude>templates/**</exclude>
                        <!-- 自定义自己的配置文件 -->
                        <exclude>*.yml</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <!-- MANIFEST.MF 中 Class-Path 加入前缀 -->
                            <classpathPrefix>lib/</classpathPrefix>
                            <!-- jar 包不包含唯一版本标识 -->
                            <useUniqueVersions>false</useUniqueVersions>
                            <!-- 指定启动类的包路径 -->
                            <mainClass>top.tanmw.Application</mainClass>
                        </manifest>
                        <manifestEntries>
                            <!--MANIFEST.MF 中 Class-Path 加入资源文件目录 -->
                            <Class-Path>
                                config/
                                lib/dcqc-uc-oauth-sdk-3.0.0-RELEASE.jar lib/ykz-sdk-2.0.2.jar
                                lib/zwdd-sdk-java-1.2.0.jar lib/Dm18-18.jar
                            </Class-Path>
                        </manifestEntries>
                    </archive>
                    <!-- 指定打出的 jar 包路径 -->
                    <outputDirectory>${project.build.directory}</outputDirectory>
                </configuration>
            </plugin>
            <!-- 这个插件是用来复制项目依赖的 jar 包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <!-- 自定义 -->
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <!-- 复制依赖的 jar 包 -->
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- 将依赖的 jar 包复制到该路径下 -->
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <excludeArtifactIds>
                                efficient-generator,efficient-boot-cache-start,efficient-boot-swagger-start,efficient-boot-cache-start,efficient-boot-configs-start,efficient-boot-auth-start,efficient-boot-rate-start,efficient-boot-logs-start,efficient-boot-file-start,efficient-boot-ykz-start,efficient-boot-common
                            </excludeArtifactIds>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <configuration>
                    <!-- 禁用创建附加的 shadedArtifact -->
                    <shadedArtifactAttached>false</shadedArtifactAttached>
                    <!-- 禁用生成依赖缩减的POM -->
                    <createDependencyReducedPom>false</createDependencyReducedPom>

                    <!-- 最小化JAR -->
                    <minimizeJar>true</minimizeJar>

                    <!-- 过滤器 -->
                    <filters>
                        <filter>
                            <artifact>*:*</artifact>
                            <excludes>
                                <!-- 排除签名文件 -->
                                <exclude>META-INF/*.SF</exclude>
                                <exclude>META-INF/*.sf</exclude>
                                <exclude>META-INF/*.DSA</exclude>
                                <exclude>META-INF/*.dsa</exclude>
                                <exclude>META-INF/*.RSA</exclude>
                                <exclude>META-INF/*.rsa</exclude>
                                <exclude>META-INF/*.EC</exclude>
                                <exclude>META-INF/*.ec</exclude>
                                <exclude>META-INF/MSFTSIG.SF</exclude>
                                <exclude>META-INF/MSFTSIG.RSA</exclude>
                            </excludes>
                        </filter>
                    </filters>

                    <!-- 包含的依赖 -->
                    <artifactSet>
                        <includes>
                            <!-- 添加 efficient-boot 开头的依赖 -->
                            <include>com.zenith:sb-*</include>
                        </includes>
                    </artifactSet>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 这个插件是用来复制项目的静态资源 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <!-- 自定义 -->
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <goals>
                            <!-- 复制静态资源 -->
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <!--忽略编译-->
                            <nonFilteredFileExtensions>
                                <nonFilteredFileExtension>cer</nonFilteredFileExtension>
                            </nonFilteredFileExtensions>
                            <useDefaultDelimiters>false</useDefaultDelimiters>
                            <resources>
                                <resource>
                                    <!-- 指定静态资源的路径 -->
                                    <directory>src/main/resources</directory>
                                    <!-- 处理文件时替换文件中的变量-->
                                    <filtering>true</filtering>
                                    <!-- 指定需要复制的文件 -->
                                    <includes>
                                        <include>application.yml</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <!-- 指定静态资源的路径 -->
                                    <directory>src/main/resources</directory>
                                    <filtering>false</filtering>
                                    <!-- 指定需要复制的文件 -->
                                    <includes>
                                        <include>application-${env}.yml</include>
                                        <include>*.xml</include>
                                        <include>*.jks</include>
                                        <include>*.properties</include>
                                        <include>public/**</include>
                                        <include>static/**</include>
                                        <include>templates/**</include>
                                        <include>xsd/**</include>
                                        <include>*.cer</include>
                                    </includes>
                                </resource>
                            </resources>
                            <!-- 指定复制到该目录下 -->
                            <outputDirectory>${project.build.directory}/config</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--     打包 zip    -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <!-- 这个插件需要指定一个配置文件 -->
                    <descriptors>
                        <descriptor>src/main/resources/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <!-- 自定义 -->
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <!-- 只执行一次 -->
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
