<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0
                      http://maven.apache.org/xsd/assembly-1.1.0.xsd">
    <id>package</id>
    <formats>
        <!-- 可以根据自己的需求定义压缩文件的格式 -->
        <format>zip</format>
    </formats>
    <!-- 不包含根目录，如果为 true，生成的压缩文件会有一个根目录 -->
    <includeBaseDirectory>false</includeBaseDirectory>
    <!-- 指定需要压缩的文件清单 -->
    <fileSets>
        <fileSet>
            <!-- 指定你需要压缩的文件目录 -->
            <directory>${project.build.directory}/lib/</directory>
            <!-- 指定压缩后的文件目录 -->
            <outputDirectory>lib</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/config/</directory>
            <outputDirectory>config</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}/</directory>
            <includes>
                <include>${project.name}-${project.version}.jar</include>
            </includes>
            <outputDirectory>/</outputDirectory>
        </fileSet>
        <!-- 自定义 shell 脚本存放路径 -->
        <fileSet>
            <directory>${project.basedir}/shell/</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>*.sh</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>