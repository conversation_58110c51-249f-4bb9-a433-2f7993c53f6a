package com.efficient.form.controller;

import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import com.efficient.common.validate.QueryGroup;
import com.efficient.form.api.DynamicFormsDataService;
import com.efficient.form.model.dto.DynamicFormsDataDTO;
import com.efficient.form.model.dto.DynamicFormsDataListDTO;
import com.efficient.form.model.entity.DynamicFormsData;
import com.efficient.form.model.vo.DynamicFormsDataVO;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 系统动态表单-表单数据 controller 层
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-12 15:09:51
 */
@RestController
@RequestMapping("/dynamicFormsData")
@Validated
@Api(tags = "系统动态表单-表单数据")
@Permission
public class DynamicFormsDataController {

    @Autowired
    private DynamicFormsDataService dynamicFormsDataService;

    /**
     * 新增
     */
    @Log(logOpt = LogEnum.SAVE, module = "系统动态表单-表单数据")
    @PostMapping("/save")
    @ApiOperation(value = "保存")
    public Result<DynamicFormsData> save(@Validated @RequestBody DynamicFormsDataDTO dto) {
        return dynamicFormsDataService.save(dto);
    }

    /**
     * 详情
     */
    @Log(logOpt = LogEnum.QUERY, module = "系统动态表单-表单数据")
    @GetMapping("/find")
    @ApiOperation(value = "详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<DynamicFormsDataVO> find(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return dynamicFormsDataService.findById(id);
    }

    /**
     * 修改
     */
    @Log(logOpt = LogEnum.UPDATE, module = "系统动态表单-表单数据")
    @PostMapping("/update")
    @ApiOperation(value = "修改")
    public Result<Boolean> update(@Validated @RequestBody DynamicFormsDataDTO dto) {
        return dynamicFormsDataService.update(dto);
    }

    /**
     * 删除
     */
    @Log(logOpt = LogEnum.DELETE, module = "系统动态表单-表单数据")
    @GetMapping("/delete")
    @ApiOperation(value = "删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据唯一标识", required = true)
    })
    public Result<Boolean> delete(@NotBlank(message = "id 不能为空") @RequestParam(name = "id") String id) {
        return dynamicFormsDataService.delete(id);
    }

    /**
     * 列表
     */
    @Log(logOpt = LogEnum.PAGE, module = "系统动态表单-表单数据")
    @PostMapping("/list")
    @ApiOperation(value = "列表", response = Result.class)
    public Result list(@Validated(value = QueryGroup.class) @RequestBody DynamicFormsDataListDTO dto) {
        return Result.ok(dynamicFormsDataService.list(dto));
    }

}
