package com.efficient.openapi.api;

import com.efficient.common.result.Result;
import com.efficient.openapi.entity.OpenApiAccessTokenRequest;
import com.efficient.openapi.entity.SSOUserInfo;

/**
 * <AUTHOR>
 * @since 2024/4/16 16:16
 */
public interface OpenApiService {
    Result<String> getAccessToken(OpenApiAccessTokenRequest tokenRequest);

    Result<SSOUserInfo> getUserInfo();

    Result<String> createToken(String userId, String appCode);

    Result<String> createReverseToken(String userId, String appCode);
}
