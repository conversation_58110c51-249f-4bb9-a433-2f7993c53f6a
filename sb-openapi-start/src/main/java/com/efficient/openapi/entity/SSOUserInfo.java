package com.efficient.openapi.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 返回第三方单点登录用户信息
 *
 * <AUTHOR>
 * @since 2024/11/14 15:55
 */
@Data
@ApiModel("返回第三方单点登录用户信息-SSOUserInfo")
public class SSOUserInfo {
    @ApiModelProperty(value = "用户主键")
    private String userId;
    /**
     * 用户单位ID，针对用户多部门任职
     */
    @ApiModelProperty(value = "用户单位ID，针对用户多部门任职")
    private String userUnitId;
    @ApiModelProperty(value = "用户单位职务")
    private String userUnitPost;
    @ApiModelProperty(value = "政务钉Id")
    private String zwddId;
    @ApiModelProperty(value = "账号")
    private String account;
    @ApiModelProperty(value = "用户名")
    private String username;
}
