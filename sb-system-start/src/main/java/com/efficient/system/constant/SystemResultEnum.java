// package com.efficient.system.constant;
//
// import com.efficient.common.result.ResultConstant;
//
// /**
//  * <AUTHOR>
//  * @since 2022/8/26 10:16
//  */
// public enum SystemResultEnum implements ResultConstant {
//     OLD_PASSWORD_NOT_NULL(9400, "旧密码不能为空！"),
//     OLD_PASSWORD_NOT_EQ(9401, "旧密码不匹配！"),
//     EXIST_CHILD_DATA(9490, "存在下级数据"),
//     EXIST_ZWDD_ID(9489, "已存在政务钉Id"),
//     EXIST_ACCOUNT(9488, "已存在账号"),
//     EXIST_ID_CARD(9487, "已存在身份证号"),
//     EXIST_PHONE(9486, "已存在电话号码")
//     ;
//
//     private int code;
//     private String msg;
//
//     SystemResultEnum(int code, String msg) {
//         this.code = code;
//         this.msg = msg;
//     }
//
//     @Override
//     public int getCode() {
//         return code;
//     }
//
//     @Override
//     public void setCode(int code) {
//         this.code = code;
//     }
//
//     @Override
//     public String getMsg() {
//         return msg;
//     }
//
//     @Override
//     public void setMsg(String msg) {
//         this.msg = msg;
//     }
// }
