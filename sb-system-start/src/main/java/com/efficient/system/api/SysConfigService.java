package com.efficient.system.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.efficient.system.model.dto.SysConfigDTO;
import com.efficient.system.model.entity.SysConfig;

/**
 * <p>
 * 系统配置 服务Api
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-26 10:57:51
 */
public interface SysConfigService extends IService<SysConfig> {
    /***
     * 新增
     */
    Result<SysConfig> save(SysConfigDTO dto);

    /**
     * 详情
     */
    <T> T findByCode(String id, Class<T> tClass);

}
