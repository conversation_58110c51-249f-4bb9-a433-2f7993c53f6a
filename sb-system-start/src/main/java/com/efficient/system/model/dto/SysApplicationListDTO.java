package com.efficient.system.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 系统第三方应用 列表查询DTO
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-16 16:47:29
 */
@Data
@ApiModel("系统第三方应用 列表查询-SysApplicationListDTO")
public class SysApplicationListDTO implements Serializable {
    private static final long serialVersionUID = 5022098475857494588L;
    @NotNull(message = "pageNum 不能为空")
    private Integer pageNum;
    @NotNull(message = "pageSize 不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "关键字搜索")
    private String keyword;
}
