{"groups": [{"name": "com.efficient.logs", "type": "com.efficient.logs.properties.LogsProperties", "sourceType": "com.efficient.logs.config.LogsConfig"}], "properties": [{"name": "com.efficient.logs.db", "type": "java.lang.Bo<PERSON>an", "description": "是否存储数据库", "defaultValue": false, "sourceType": "com.efficient.logs.properties.LogsProperties"}, {"name": "com.efficient.logs.level", "type": "java.lang.String", "description": "日志等级", "defaultValue": "info", "sourceType": "com.efficient.logs.properties.LogsProperties"}, {"name": "com.efficient.logs.name", "type": "java.lang.String", "description": "日志文件名称前缀", "defaultValue": "log", "sourceType": "com.efficient.logs.properties.LogsProperties"}, {"name": "com.efficient.logs.systemIdField", "type": "java.lang.String", "description": "系统ID字段", "defaultValue": "systemId", "sourceType": "com.efficient.logs.properties.LogsProperties"}, {"name": "com.efficient.logs.path", "type": "java.lang.String", "description": "日志存储路径", "defaultValue": "/efficient/logs/", "sourceType": "com.efficient.logs.properties.LogsProperties"}, {"name": "com.efficient.logs.sql.level", "type": "java.lang.String", "description": "sql日志等级", "defaultValue": "debug", "sourceType": "com.efficient.logs.properties.LogSqlProperties"}, {"name": "com.efficient.logs.sql.daoPackage", "type": "java.lang.String", "description": "dao包的路径", "defaultValue": "com.efficient", "sourceType": "com.efficient.logs.properties.LogSqlProperties"}, {"name": "com.efficient.logs.sql.showMethod", "type": "java.lang.Bo<PERSON>an", "description": "是否展示方法名", "defaultValue": false, "sourceType": "com.efficient.logs.properties.LogSqlProperties"}, {"name": "com.efficient.logs.sql.showSql", "type": "java.lang.Bo<PERSON>an", "description": "是否展示SQL", "defaultValue": true, "sourceType": "com.efficient.logs.properties.LogSqlProperties"}, {"name": "com.efficient.logs.sql.showElapsed", "type": "java.lang.Bo<PERSON>an", "description": "是否展示执行耗时", "defaultValue": false, "sourceType": "com.efficient.logs.properties.LogSqlProperties"}, {"name": "com.efficient.logs.sql.showRows", "type": "java.lang.Bo<PERSON>an", "description": "是否展示结果行数", "defaultValue": false, "sourceType": "com.efficient.logs.properties.LogSqlProperties"}], "hints": [{"name": "com.efficient.logs.level", "values": [{"value": "trace"}, {"value": "debug"}, {"value": "info"}, {"value": "warn"}, {"value": "error"}, {"value": "fatal"}, {"value": "off"}]}]}