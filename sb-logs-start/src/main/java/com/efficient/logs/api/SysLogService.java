package com.efficient.logs.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.common.result.Result;
import com.efficient.logs.annotation.Log;
import com.efficient.logs.constant.LogEnum;
import com.efficient.logs.model.dto.SysLogListDTO;
import com.efficient.logs.model.entity.SysLog;
import com.efficient.logs.model.vo.SysLogVO;

/**
 * <p>
 * 服务Api
 * </p>
 *
 * <AUTHOR> generator
 * @date 2022-09-05 16:24:37
 */
public interface SysLogService extends IService<SysLog> {

    boolean saveLog(Log log, String ip, String desc, String url, String params, String resultCode, String result, String exception);

    void saveLogAsync(String module, LogEnum logEnum, String desc, Result result);

    Page<SysLogVO> list(SysLogListDTO dto);

    SysLog find(String id);
}
