package com.efficient.logs.util;

import cn.hutool.core.util.StrUtil;
import com.efficient.common.util.JackSonUtil;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/10 14:07
 */
public class RequestParaUtil {

    public static String getPara(HttpServletRequest request) throws Exception {
        // 如果是文件上传请求
        if (StrUtil.contains(request.getContentType(), "multipart/form-data")) {
            // 处理文件上传逻辑，返回文件参数或其他内容
            return handleMultipartFormData(request);
        }

        // 如果是 POST 请求
        if (StrUtil.endWithIgnoreCase(request.getMethod(), "post")) {
            // 读取请求体
            return handlePostRequest(request);
        }

        // 如果是 GET 请求
        else if (StrUtil.endWithIgnoreCase(request.getMethod(), "get")) {
            // 获取查询参数并转化为 JSON 格式
            return handleGetRequest(request);
        }

        return null;
    }

    // 处理文件上传请求（multipart/form-data）
    private static String handleMultipartFormData(HttpServletRequest request) throws IOException, ServletException {
        // 处理文件上传的表单字段和文件
        Map<String, String[]> parameterMap = request.getParameterMap();
        // // 你可以通过 request.getParts() 获取上传的文件
        // Collection<Part> parts = request.getParts();
        // 进一步处理文件和表单字段，如果需要可以返回文件和表单字段的 JSON 格式
        return JackSonUtil.toJson(parameterMap);
    }

    // 处理 POST 请求
    private static String handlePostRequest(HttpServletRequest request) throws IOException {
        // 使用 BufferedReader 读取请求体内容
        return request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
    }

    // 处理 GET 请求
    private static String handleGetRequest(HttpServletRequest request) {
        // 获取 GET 请求的参数，并将其转换为 JSON 字符串
        Map<String, String[]> parameterMap = request.getParameterMap();
        // 处理多个值的情况，默认取第一个值
        Map<String, String> simpleParamMap = parameterMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue()[0])); // 获取第一个值
        return JackSonUtil.toJson(simpleParamMap);
    }

}
