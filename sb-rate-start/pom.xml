<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zenith</groupId>
        <artifactId>sb</artifactId>
        <version>2.2.1-20250728-02-beta</version>
    </parent>
    <description>接口幂等性校验</description>
    <artifactId>sb-rate-start</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-cache-start</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>sb-common</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>