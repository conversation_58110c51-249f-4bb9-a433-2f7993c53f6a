{"groups": [{"name": "com.efficient.rate", "type": "com.efficient.rate.properties.RateProperties", "sourceType": "com.efficient.rate.config.RateConfig"}], "properties": [{"name": "com.efficient.rate.global", "type": "java.lang.Bo<PERSON>an", "description": "是否启用全局幂等性校验,启用后所有接口都会校验幂等性", "defaultValue": false, "sourceType": "com.efficient.rate.properties.RateProperties"}, {"name": "com.efficient.rate.expireTime", "type": "java.lang.Long", "description": "全局幂等性校验间隔时间，设置后，com.efficient.rate.annotation.rate的过期时间将会失效，最小1秒", "defaultValue": 1, "sourceType": "com.efficient.rate.properties.RateProperties"}, {"name": "com.efficient.rate.methodList", "type": "java.util.List", "description": "拦截方法名称", "defaultValue": "**/save**,**/add**,**/insert**", "sourceType": "com.efficient.rate.properties.RateProperties"}, {"name": "com.efficient.rate.excludeApiList", "type": "java.util.List", "description": "排除拦截方法名称，针对特定包含在methodList中能匹配到的", "defaultValue": "", "sourceType": "com.efficient.rate.properties.RateProperties"}]}