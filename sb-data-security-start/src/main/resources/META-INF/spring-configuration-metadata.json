{"groups": [{"name": "com.efficient.data.security", "type": "com.efficient.data.security.properties.DataSecurityProperties", "sourceType": "com.efficient.data.security.config.DataSecurityConfig"}], "properties": [{"name": "com.efficient.security.api.requestEnable", "type": "java.lang.Bo<PERSON>an", "description": "是针对请求参数进行解密", "defaultValue": true, "sourceType": "com.efficient.data.security.properties.DataSecurityProperties"}, {"name": "com.efficient.security.api.requestEnableType", "type": "com.efficient.data.security.constant.EnableType", "description": "加解密是否需要注解配置", "defaultValue": "EnableType.NODE", "sourceType": "com.efficient.data.security.properties.DataSecurityProperties"}, {"name": "com.efficient.auth.whiteList", "type": "java.util.List", "description": "请求加密过滤白名单", "defaultValue": "swagger", "sourceType": "com.efficient.data.security.properties.DataSecurityProperties"}, {"name": "com.efficient.security.api.responseEnable", "type": "java.lang.Bo<PERSON>an", "description": "是针对返回值进行加密", "defaultValue": true, "sourceType": "com.efficient.data.security.properties.DataSecurityProperties"}, {"name": "com.efficient.security.api.responseEnableType", "type": "com.efficient.data.security.constant.EnableType", "description": "加解密是否需要注解配置", "defaultValue": "EnableType.NODE", "sourceType": "com.efficient.data.security.properties.DataSecurityProperties"}, {"name": "com.efficient.auth.whiteList", "type": "java.util.List", "description": "返回加密过滤白名单", "defaultValue": "", "sourceType": "com.efficient.data.security.properties.DataSecurityProperties"}, {"name": "com.efficient.security.api.encryptKey", "type": "java.lang.String", "description": "加密key", "defaultValue": "http://tanmw.top", "sourceType": "com.efficient.data.security.properties.DataSecurityProperties"}, {"name": "com.efficient.security.db.dbEncryptEnable", "type": "java.lang.Bo<PERSON>an", "description": "加密key", "defaultValue": true, "sourceType": "com.efficient.data.security.properties.DataSecurityProperties"}, {"name": "com.efficient.security.db.dbEncryptKey", "type": "java.lang.String", "description": "Db加密key", "defaultValue": "http://tanmw.top", "sourceType": "com.efficient.data.security.properties.DataSecurityProperties"}, {"name": "com.efficient.security.db.dbEncryptModelPath", "type": "java.lang.String", "description": "加解密的实体类全限定名路径，多个用逗号隔开，比如com.zenith.front.model,会自动加载该路径下需要加解密的类", "defaultValue": "top.tanmw.demo.model", "sourceType": "com.efficient.data.security.properties.DataSecurityProperties"}]}