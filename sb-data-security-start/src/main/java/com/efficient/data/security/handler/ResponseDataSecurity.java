package com.efficient.data.security.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.efficient.common.exception.DataSecurityException;
import com.efficient.common.result.Result;
import com.efficient.data.security.annotation.ResponseEncrypt;
import com.efficient.data.security.annotation.SecuritySkip;
import com.efficient.data.security.constant.EnableType;
import com.efficient.data.security.properties.DataSecurityProperties;
import com.efficient.data.security.util.AESUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/6/8 17:38
 */
@ConditionalOnProperty(name = "com.efficient.security.api.responseEnable", havingValue = "true")
@ControllerAdvice
@Slf4j
public class ResponseDataSecurity implements ResponseBodyAdvice<Object> {
    @Autowired
    private DataSecurityProperties properties;
    @Autowired
    private AESUtils aesUtils;

    @Override
    public boolean supports(MethodParameter methodParameter, Class converterType) {
        SecuritySkip skip = methodParameter.getMethodAnnotation(SecuritySkip.class);
        if (Objects.nonNull(skip) && skip.skipResponse()) {
            return false;
        }

        if (Objects.equals(properties.getApi().getResponseEnableType(), EnableType.NEED)) {
            return methodParameter.hasMethodAnnotation(ResponseEncrypt.class);
        } else return Objects.equals(properties.getApi().getResponseEnableType(), EnableType.NODE);
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        try {
            if (body instanceof Result) {
                String servletPath = ((ServletServerHttpRequest) request).getServletRequest().getServletPath();
                List<String> responseWhiteList = properties.getApi().getResponseWhiteList();
                if (CollUtil.isNotEmpty(responseWhiteList) && responseWhiteList.stream()
                        .anyMatch(servletPath::contains)) {
                    return body;
                }
                Result result = (Result) body;
                Object bodyData = result.getData();
                if (bodyData != null && Objects.equals(Result.ok().getCode(), result.getCode())) {
                    result.setData(aesUtils.encrypt(JSONUtil.toJsonStr(bodyData)));
                }
                return result;
            }
        } catch (Exception e) {
            throw new DataSecurityException(e);
        }
        return body;
    }
}
