package com.efficient.data.security.properties;

import com.efficient.data.security.constant.EnableType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/8 16:57
 */
@ConfigurationProperties("com.efficient.security")
@Data
public class DataSecurityProperties {

    private Api api = new Api();
    private Db db = new Db();

    @Data
    public class Api {
        /**
         * 是否针对请求参数进行解密
         */
        private boolean requestEnable = false;
        /**
         * 请求值加密类型
         */
        private EnableType requestEnableType = EnableType.NODE;
        /**
         * 请求加密过滤
         */
        private List<String> requestWhiteList = new ArrayList<String>() {
            private static final long serialVersionUID = -6831198909191678412L;

            {
                add("swagger");
            }
        };
        /**
         * 是否针对返回参数进行加密
         */
        private boolean responseEnable = false;
        /**
         * 返回值加密类型
         */
        private EnableType responseEnableType = EnableType.NODE;
        /**
         * 返回加密过滤
         */
        private List<String> responseWhiteList = new ArrayList<String>() {
            private static final long serialVersionUID = -6831198909191678412L;

            {
                // add("**swagger**");
            }
        };
        /**
         * AES KEY
         */
        private String encryptKey = "http://tanmw.top";
    }

    @Data
    public class Db {
        /**
         * 是否启用数据库加密
         */
        private boolean dbEncryptEnable = false;
        /**
         * db AES KEY
         */
        private String dbEncryptKey = "http://tanmw.top";
        /**
         * 加解密的实体类全限定名路径，多个用逗号隔开，比如com.zenith.front.model
         */
        private String dbEncryptModelPath = "top.tanmw.demo.model";
    }

}
