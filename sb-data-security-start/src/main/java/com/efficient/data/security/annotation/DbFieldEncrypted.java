package com.efficient.data.security.annotation;

import java.lang.annotation.*;

/**
 * 数据库加密
 *
 * <AUTHOR>
 * 需要搭配使用
 * @TableField(value = "id_card",typeHandler = com.efficient.data.security.db.typehandler.EncryptTypeHandler.class)
 * @DbFieldEncrypted private String idCard;
 * @since 2023/7/10 15:17
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
public @interface DbFieldEncrypted {

}
