package com.efficient.irs.util;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

@Slf4j
public class TokenSignUtils {
    private static final String ACCESS_KEY_PARAM = "access_key";
    private static final String SIGNATURE_PARAM = "signature";
    private static final String TIMESTAMP_PARAM = "timestamp";

    public static Map<String, Object> getTokenParams(String accessKey, String secretKey) {
        long timestamp = System.currentTimeMillis() / 1000;
        Map<String, Object> params = new TreeMap<>();
        params.put(ACCESS_KEY_PARAM, accessKey);
        params.put(TIMESTAMP_PARAM, String.valueOf(timestamp));
        String signString = params.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining("&"));
        params.put(SIGNATURE_PARAM, signWithSHA256(signString, secretKey));
        return params;
    }

    private static String signWithSHA256(String signString, String secret) {
        try {
            Mac macSHA256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            macSHA256.init(secretKey);
            byte[] hash = macSHA256.doFinal(signString.getBytes());
            return byteToHex(hash);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static String byteToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                sb.append("0");
            }
            sb.append(temp);
        }
        return sb.toString();
    }
}
