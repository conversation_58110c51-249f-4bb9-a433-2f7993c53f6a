package com.efficient.irs.util;

import cn.hutool.core.util.StrUtil;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class AESUtils {
    private static final String ENCODING = "UTF-8";
    private static final String AES_ALGORITHM = "AES";
    private static final String CIPHER_CBC_PADDING = "AES/CBC/PKCS5Padding";

    /**
     * AES_CBC 加密
     *
     * @param content 待加密内容
     * @param aesKey  密码
     * @return 加密内容
     */
    public static String encryptCBC(String content, String aesKey) {
        if (StrUtil.isBlank(content)) {
            return null;
        }
        // 判断秘钥是否为 16 位
        if (StrUtil.isNotBlank(aesKey) && aesKey.length() == 16) {
            try {
                // 对密码进行编码
                byte[] bytes = aesKey.getBytes(ENCODING);
                // 设置加密算法，生成秘钥
                SecretKeySpec skeySpec = new SecretKeySpec(bytes, AES_ALGORITHM);
                // "算法/模式/补码方式"
                Cipher cipher = Cipher.getInstance(CIPHER_CBC_PADDING);
                // 偏移
                IvParameterSpec iv = new IvParameterSpec(aesKey.getBytes(ENCODING));
                // 选择加密
                cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
                // 根据待加密内容生成字节数组
                byte[] encrypted = cipher.doFinal(content.getBytes(ENCODING));
                // 返回 base64 字符串
                return Base64Utils.encodeToString(encrypted);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return null;
        }
    }

    /**
     * AES_CBC 解密
     *
     * @param content 待解密内容
     * @param aesKey  密码
     * @return 解密内容
     */
    public static String decryptCBC(String content, String aesKey) {
        if (StrUtil.isBlank(content)) {
            return null;
        }
        //判断秘钥是否为 16 位
        if (StrUtil.isNotBlank(aesKey) && aesKey.length() == 16) {
            try {
                // 对密码进行编码
                byte[] bytes = aesKey.getBytes(ENCODING);
                // 设置解密算法，生成秘钥
                SecretKeySpec skeySpec = new SecretKeySpec(bytes, AES_ALGORITHM);
                // 偏移
                IvParameterSpec iv = new IvParameterSpec(aesKey.getBytes(ENCODING));
                // "算法/模式/补码方式"
                Cipher cipher = Cipher.getInstance(CIPHER_CBC_PADDING);
                // 选择解密
                cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
                // 先进行 Base64 解码
                byte[] decodeBase64 = Base64Utils.decodeFromString(content);
                // 根据待解密内容进行解密
                byte[] decrypted = cipher.doFinal(decodeBase64);
                // 将字节数组转成字符串
                return new String(decrypted, ENCODING);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return null;
        }
    }
}
