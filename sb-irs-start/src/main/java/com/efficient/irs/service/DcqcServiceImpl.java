package com.efficient.irs.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.efficient.common.util.JackSonUtil;
import com.efficient.irs.api.DcqcService;
import com.efficient.irs.properties.DcqcProperties;
import com.efficient.irs.util.DcqcSm4Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/1/7 14:12
 */
@Service
@Slf4j
public class DcqcServiceImpl implements DcqcService {
    @Autowired
    private DcqcProperties dcqcProperties;

    @Override
    public String parsingToken(String token) throws Exception {
        String accessToken = this.getAccessToken();
        String accountId = this.getAccountId(token, accessToken);
        log.info("ssoLogin accountId={}", accountId);
        Base64.Decoder decoder = Base64.getDecoder();
        String zwddId = new String(DcqcSm4Utils.decrypt_Ecb_Padding(dcqcProperties.getAppSecret(), decoder.decode(accountId)));
        log.info("ssoLogin zwddId={}", zwddId);
        return zwddId;
    }

    @Override
    public String getAccessToken() {
        String url = dcqcProperties.getUrl() + "/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/third-api/access-token";
        String body = HttpRequest.get(url).form("secret", dcqcProperties.getAppSecret())
                .header("API-TOKEN", this.getApiToken())
                .header("app-key", dcqcProperties.getAppKey())
                .header("request-id", IdUtil.fastSimpleUUID())
                .execute().body();
        JSONObject entries = JSONUtil.parseObj(body);
        log.info("getAccessToken-body:{}", body);
        Object code = entries.get("code");
        if (!Objects.equals(code, 200)) {
            throw new RuntimeException("获取三级治理AccessToken失败");
        }
        JSONObject data = entries.getJSONObject("data");
        return data.getJSONObject("data").getStr("accessToken");
    }

    @Override
    public String getAccountId(String token, String accessToken) {
        log.info("token:{},accessToken:{}", token, accessToken);
        String url = dcqcProperties.getUrl() + "/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/third-api/account-id?token=" + token + "&accessToken=" + accessToken;
        String body = HttpRequest.get(url)
                .header("API-TOKEN", this.getApiToken())
                .header("app-key", dcqcProperties.getAppKey())
                .header("request-id", IdUtil.fastSimpleUUID())
                .execute().body();
        JSONObject entries = JSONUtil.parseObj(body);
        log.info("getAccountId-body:{}", body);
        Object code = entries.get("code");
        if (!Objects.equals(code, 200)) {
            throw new RuntimeException("获取三级治理AccountId失败");
        }
        JSONObject data = entries.getJSONObject("data");
        return data.getJSONObject("data").getStr("accountId");
    }

    @Override
    public String getApiToken() {
        String url = dcqcProperties.getUrl() + "/dcqc/dcqc-gateway/dcqc-system/appKeySecret/apply/keyTokenByKey";
        Map<String, String> param = new HashMap<>();
        param.put("authKey", dcqcProperties.getAppKey());
        param.put("authSecret", dcqcProperties.getAppSecret());
        param.put("username", dcqcProperties.getUsername());
        String body = HttpRequest.post(url).body(JackSonUtil.toJson(param))
                .contentType("application/json")
                .execute().body();
        JSONObject entries = JSONUtil.parseObj(body);
        log.info("getApiToken-body:{}", body);
        Object code = entries.get("code");
        if (!Objects.equals(code, 200)) {
            throw new RuntimeException("获取三级治理ApiToken失败");
        }
        return entries.getStr("data");
    }
}
