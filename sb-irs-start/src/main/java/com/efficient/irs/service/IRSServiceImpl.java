package com.efficient.irs.service;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.efficient.cache.api.CacheUtil;
import com.efficient.cache.constant.CacheConstant;
import com.efficient.common.result.Result;
import com.efficient.irs.api.IRSService;
import com.efficient.irs.constant.IRSConstant;
import com.efficient.irs.properties.IRSProperties;
import com.efficient.irs.util.AESUtils;
import com.efficient.irs.util.TokenSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * irs 服务
 *
 * <AUTHOR>
 * @since 2024/11/18 16:19
 */
@Slf4j
@Service
public class IRSServiceImpl implements IRSService {

    private static final String EXPIRE_TIME = "expire_time";
    private static final String TOKEN = "token";

    @Resource
    private CacheUtil cacheUtil;

    @Resource
    private IRSProperties irsProperties;

    @Override
    public String getToken() {
        Map<String, Object> cacheMap = cacheUtil.get(CacheConstant.OTHER_CACHE, IRSConstant.TOKEN_SIGN_CACHE);
        if (ObjectUtil.isNull(cacheMap)) {
            cacheMap = getTokenCacheMap();
        }
        long expireTime = (long) cacheMap.get(EXPIRE_TIME);
        long currentTime = System.currentTimeMillis() / 1000;
        if (expireTime - currentTime <= 0) {
            cacheMap = getTokenCacheMap();
        }
        return cacheMap.get(TOKEN).toString();
    }

    @Override
    public String encryptCBC(String content) {
        return AESUtils.encryptCBC(content, irsProperties.getAesKey());
    }

    @Override
    public String decryptCBC(String content) {
        return AESUtils.decryptCBC(content, irsProperties.getAesKey());
    }

    private Map<String, Object> getTokenCacheMap() {
        JSONObject data = sendTokenRequest();
        Map<String, Object> cacheMap = MapUtil.newHashMap();
        cacheMap.put(EXPIRE_TIME, data.get(EXPIRE_TIME));
        cacheMap.put(TOKEN, data.get(TOKEN));
        int expireTime = irsProperties.getExpireTime() * 60;
        this.cacheUtil.put(CacheConstant.OTHER_CACHE, IRSConstant.TOKEN_SIGN_CACHE, data, expireTime);
        return cacheMap;
    }

    private JSONObject sendTokenRequest() {
        String accessKey = irsProperties.getAccessKey();
        String secretKey = irsProperties.getSecretKey();
        String getTokenUrl = irsProperties.getHost() + irsProperties.getTokenUrl();
        Map<String, Object> tokenParams = TokenSignUtils.getTokenParams(accessKey, secretKey);
        Result<?> result = null;
        try {
            String body = HttpRequest.get(getTokenUrl)
                    .form(tokenParams)
                    .execute()
                    .body();
            result = JSONUtil.toBean(body, Result.class);
        } catch (Exception e) {
            log.error("请求token失败");
        }
        if (ObjectUtil.isNull(result) || (ObjectUtil.isNotNull(result) && result.getCode() != 200)) {
            log.error("请求token失败");
            throw new RuntimeException("请求token失败");
        }
        JSONObject data = JSONUtil.parseObj(result.getData());
        if (ObjectUtil.isNull(data)) {
            throw new RuntimeException("请求token失败");
        }
        return data;
    }
}
