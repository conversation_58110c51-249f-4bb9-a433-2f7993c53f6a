package com.efficient.irs.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2022/8/26 9:59
 */
@ConfigurationProperties("com.efficient.irs")
@Data
public class IRSProperties {

    private String accessKey;
    private String secretKey;
    /**
     * 加解密密钥
     */
    private String aesKey;
    /**
     * token 缓存过期时间
     */
    private Integer expireTime = 90;
    /**
     * irs 服务地址
     * nameserver ***********
     * nameserver ***********
     */
    private String host = "http://drs.dsjfzj.cq.cegn.cn";
    /**
     * token 请求地址
     */
    private String tokenUrl;
}
