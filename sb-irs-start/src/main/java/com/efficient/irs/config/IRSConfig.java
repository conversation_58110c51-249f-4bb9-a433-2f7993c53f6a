package com.efficient.irs.config;

import com.efficient.irs.properties.IRSProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/8/28 18:10
 */
@Configuration
@EnableConfigurationProperties(IRSProperties.class)
@Slf4j
public class IRSConfig {
    @Autowired
    private IRSProperties taskProperties;

}
