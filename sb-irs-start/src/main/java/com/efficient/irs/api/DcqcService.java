package com.efficient.irs.api;

/**
 * 三级治理中心
 *
 * <AUTHOR>
 * @since 2025/1/7 14:11
 */
public interface DcqcService {
    /**
     * 解析token获取政务钉钉ID
     *
     * @param token
     * @return
     * @throws Exception
     */
    String parsingToken(String token) throws Exception;

    /**
     * 获取三级治理中心 AccessToken
     * @return
     */
    String getAccessToken();
    /**
     * 获取三级治理中心 AccountId
     * @return
     */
    String getAccountId(String token, String accessToken);
    /**
     * 获取三级治理中心 ApiToken
     * @return
     */
    String getApiToken();
}
