{"groups": [{"name": "com.efficient.camera", "type": "com.efficient.camera.properties.CameraProperties", "sourceType": "com.efficient.camera.config.CameraConfig"}], "properties": [{"name": "com.efficient.camera.active", "type": "java.lang.String", "description": "摄像头类型", "defaultValue": "hkws", "sourceType": "com.efficient.camera.properties.CameraProperties"}, {"name": "com.efficient.camera.hkws.host", "type": "java.lang.String", "description": "请求地址", "defaultValue": "127.0.0.1:443", "sourceType": "com.efficient.camera.properties.CameraProperties.HkwsProperties"}, {"name": "com.efficient.camera.hkws.protocol", "type": "java.lang.String", "description": "协议，默认 https", "defaultValue": "https", "sourceType": "com.efficient.camera.properties.CameraProperties.HkwsProperties"}, {"name": "com.efficient.camera.hkws.appKey", "type": "java.lang.String", "description": "appKey", "defaultValue": "", "sourceType": "com.efficient.camera.properties.CameraProperties.HkwsProperties"}, {"name": "com.efficient.camera.hkws.appSecret", "type": "java.lang.String", "description": "appKey", "defaultValue": "", "sourceType": "com.efficient.camera.properties.CameraProperties.HkwsProperties"}, {"name": "com.efficient.camera.hkws.artemisPath", "type": "java.lang.String", "description": "默认统一请求前缀", "defaultValue": "/artemis", "sourceType": "com.efficient.camera.properties.CameraProperties.HkwsProperties"}, {"name": "com.efficient.camera.hkws.defaultTimeout", "type": "java.lang.Integer", "description": "默认超时时间，毫秒", "defaultValue": 10000, "sourceType": "com.efficient.camera.properties.CameraProperties.HkwsProperties"}, {"name": "com.efficient.camera.hkws.socketTimeout", "type": "java.lang.Integer", "description": "socket超时时间，毫秒", "defaultValue": 60000, "sourceType": "com.efficient.camera.properties.CameraProperties.HkwsProperties"}], "hints": [{"name": "com.efficient.camera.active", "values": [{"value": "hkws", "description": "hkws"}]}]}