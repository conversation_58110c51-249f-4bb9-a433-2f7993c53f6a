package com.efficient.camera.api;

import com.baomidou.mybatisplus.extension.service.IService;
import com.efficient.camera.model.dto.HkwsControllingDTO;
import com.efficient.camera.model.dto.HkwsEventDTO;
import com.efficient.camera.model.entity.CameraHkws;
import com.efficient.common.result.Result;

import java.net.MalformedURLException;

/**
 * <AUTHOR>
 * @since 2025/1/13 10:04
 */
public interface HkwsService extends IService<CameraHkws> {
    Result callPostApiGetResources();

    String callPostApiPreviewUrl(String cameraIndexCode, String protocol)  throws Exception ;

    Result getEventSubscriptionByEventTypes(HkwsEventDTO hkwsEventDTO);

    Result getEventUnSubscriptionByEventTypes(HkwsEventDTO hkwsEventDTO);

    Result getEventSubscriptionView();

    Result controlling(HkwsControllingDTO dto);

    Result manualCapture(String cameraIndexCode) throws Exception;
}
