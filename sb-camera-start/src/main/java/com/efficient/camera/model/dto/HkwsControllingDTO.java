package com.efficient.camera.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 云台操作
 *
 * <AUTHOR>
 * @since 2024/10/17 9:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("海康威视云台操作")
public class HkwsControllingDTO {
    /**
     * 监控点编号，
     * 可通过分页获取监控点资源获取
     */
    @ApiModelProperty(value = "监控点编号", required = true)
    private String cameraIndexCode;
    /**
     * 0-开始 ，1-停止
     * 注：GOTO_PRESET命令下填任意值均可转到预置点,建议填0即可
     */
    @ApiModelProperty(value = "0-开始 ，1-停止", required = true)
    private Integer action;

    @ApiModelProperty(value = "不区分大小写\n" +
            "说明：\n" +
            "LEFT 左转\n" +
            "RIGHT右转\n" +
            "UP 上转\n" +
            "DOWN 下转\n" +
            "ZOOM_IN 焦距变大\n" +
            "ZOOM_OUT 焦距变小\n" +
            "LEFT_UP 左上\n" +
            "LEFT_DOWN 左下\n" +
            "RIGHT_UP 右上\n" +
            "RIGHT_DOWN 右下\n" +
            "FOCUS_NEAR 焦点前移\n" +
            "FOCUS_FAR 焦点后移\n" +
            "IRIS_ENLARGE 光圈扩大\n" +
            "IRIS_REDUCE 光圈缩小\n" +
            "WIPER_SWITCH 接通雨刷开关\n" +
            "START_RECORD_TRACK 开始记录运行轨迹\n" +
            "STOP_RECORD_TRACK 停止记录运行轨迹\n" +
            "START_TRACK 开始运行轨迹\n" +
            "STOP_TRACK 停止运行轨迹；\n" +
            "以下命令presetIndex不可为空：\n" +
            "GOTO_PRESET到预置点", required = true)
    private String command;
}
