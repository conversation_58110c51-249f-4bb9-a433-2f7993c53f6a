package com.efficient.camera.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/13 10:48
 */
@Data
@ApiModel("海康威视事件订阅请求参数")
public class HkwsEventDTO {
    /**
     * 事件订阅类型
     */
    @ApiModelProperty(value = "事件订阅类型")
    private List<Integer> eventTypeList;
    /**
     * 订阅事件回调地址
     */
    @ApiModelProperty(value = "订阅事件回调地址")
    private String callBackUrl;
}
