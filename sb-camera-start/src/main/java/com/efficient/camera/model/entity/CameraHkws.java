package com.efficient.camera.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/1/13 10:12
 */
@Data
@TableName("camera_hkws")
@ApiModel("海康威视摄像头信息")
public class CameraHkws implements Serializable {
    private static final long serialVersionUID = 878864145278483911L;

    /**
     *摄像头唯一标识
     */
    @ApiModelProperty(value = "摄像头唯一标识")
    @TableId(value = "cameraIndexCode")
    private String cameraIndexCode;
    /**
     *摄像头名称
     */
    @ApiModelProperty(value = "摄像头名称")
    @TableField("cameraName")
    private String cameraName;
    /**
     *内容
     */
    @ApiModelProperty(value = "内容")
    @TableField("content")
    private String content;
    /**
     *create_user
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     *create_time
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     *update_user
     */
    @ApiModelProperty(value = "修改人")
    @TableField(value = "update_user", fill = FieldFill.UPDATE)
    private String updateUser;
    /**
     *update_time
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;
    /**
     *is_delete
     */
    @ApiModelProperty(value = "是否删除")
    @TableLogic
    @TableField("is_delete")
    private Integer isDelete;

}
