package com.efficient.camera.config;

import com.efficient.camera.properties.CameraProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/1/13 9:50
 */
@Configuration
@EnableConfigurationProperties(CameraProperties.class)
@MapperScan(basePackages = {"com.efficient.camera.dao"})
public class CameraConfig {

}
