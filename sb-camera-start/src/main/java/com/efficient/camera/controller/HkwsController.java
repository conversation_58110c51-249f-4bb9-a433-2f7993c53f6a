package com.efficient.camera.controller;

import cn.hutool.core.util.StrUtil;
import com.efficient.camera.api.HkwsService;
import com.efficient.camera.model.dto.HkwsControllingDTO;
import com.efficient.camera.model.dto.HkwsEventDTO;
import com.efficient.common.permission.Permission;
import com.efficient.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/1/13 10:03
 */
@RestController
@RequestMapping("/file")
@Validated
@Api(tags = "海康威视")
@Slf4j
@Permission
public class HkwsController {
    @Autowired
    private HkwsService hkwsService;

    /**
     * 调用POST请求类型接口，分页获取监控点资源
     *
     * @return
     */
    @ApiOperation(value = "分页获取监控点资源")
    @GetMapping(value = "/getResource")
    public Result getResource() {
        return hkwsService.callPostApiGetResources();
    }

    /**
     * 调用POST请求类型接口，获取当前监控点的预览url
     *
     * @param cameraIndexCode,摄像头唯一编码
     * @param protocol,视频流协议，默认不传为：hls(HLS协议（HLS协议只支持海康SDK协议、EHOME协议、ONVIF协议接入的设备；只支持H264视频编码和AAC音频编码）；https://open.hikvision.com/docs/docId?productId=5c67f1e2f05948198c909700&version=%2Ff95e951cefc54578b523d1738f65f0a1&tagPath=API%E5%88%97%E8%A1%A8-%E8%A7%86%E9%A2%91%E5%BA%94%E7%94%A8%E6%9C%8D%E5%8A%A1-%E8%A7%86%E9%A2%91%E8%83%BD%E5%8A%9B)
     * @return
     */
    @ApiOperation(value = "获取当前监控点的预览url")
    @GetMapping(value = "/getOnePreviewUrl")
    public Result getOnePreviewUrl(@RequestParam(value = "cameraIndexCode") String cameraIndexCode, @RequestParam(value = "protocol", required = false) String protocol) throws Exception {
        if (StrUtil.isBlank(protocol)) {
            protocol = "hls";
        }
        String url = hkwsService.callPostApiPreviewUrl(cameraIndexCode, protocol);
        return Objects.nonNull(url) ? Result.ok(url) : Result.fail();
    }

    /**
     * 调用POST请求类型接口，按事件类型订阅事件
     *
     * @return
     */
    @ApiOperation(value = "按事件类型订阅事件")
    @PostMapping(value = "/eventSubscriptionByEventTypes")
    public Result eventSubscriptionByEventTypes(@RequestBody HkwsEventDTO hkwsEventDTO) {
        return hkwsService.getEventSubscriptionByEventTypes(hkwsEventDTO);
    }

    @ApiOperation(value = "按事件类型取消订阅")
    @GetMapping(value = "/eventUnSubscriptionByEventTypes")
    public Result eventUnSubscriptionByEventTypes(@RequestBody HkwsEventDTO hkwsEventDTO) {
        return hkwsService.getEventUnSubscriptionByEventTypes(hkwsEventDTO);
    }

    /**
     * 调用POST请求类型接口，查询事件订阅信息
     *
     * @return
     */
    @ApiOperation(value = "查询事件订阅信息")
    @GetMapping(value = "/getEventSubscriptionView")
    public Result getEventSubscriptionView() {
        return hkwsService.getEventSubscriptionView();
    }

    /**
     * 调用POST请求类型接口，云台操作
     *
     * @return
     */
    @ApiOperation(value = "云台操作")
    @PostMapping(value = "/controlling")
    public Result controlling(@RequestBody HkwsControllingDTO dto) {
        return hkwsService.controlling(dto);
    }

    /**
     * 调用POST请求类型接口，手动抓图
     *
     * @return
     */
    @ApiOperation(value = "手动抓图")
    @GetMapping(value = "/manualCapture")
    public Result manualCapture(@RequestParam("cameraIndexCode") String cameraIndexCode) throws Exception {
        return hkwsService.manualCapture(cameraIndexCode);
    }
}
