package com.efficient.camera.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 对接文档：https://open.hikvision.com/docs/docId?productId=5c67f1e2f05948198c909700&version=%2F29c78ef52ca842c7933bd2b8e051e9d0
 *
 * <AUTHOR>
 * @since 2025/1/13 9:51
 */
@ConfigurationProperties("com.efficient.camera")
@Data
public class CameraProperties {
    /**
     * 摄像头类型
     */
    private String active = "hkws";
    /**
     * 海康威视
     */
    private HkwsProperties hkws = new HkwsProperties();

    @Data
    public static class HkwsProperties {
        // ip+端口
        private String host;
        // 协议
        private String protocol = "https";
        /**
         * AK
         */
        private String appKey;
        /**
         * SK
         */
        private String appSecret;
        /**
         * 统一前缀
         */
        private String artemisPath = "/artemis";
        /**
         * 默认超时时间
         */
        private Integer defaultTimeout = 10000;
        /**
         * socket超时时间
         */
        private Integer socketTimeout = 60000;

    }
}
