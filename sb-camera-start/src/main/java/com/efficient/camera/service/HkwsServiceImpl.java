package com.efficient.camera.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.efficient.camera.api.HkwsService;
import com.efficient.camera.dao.CameraHkwsMapper;
import com.efficient.camera.model.dto.HkwsControllingDTO;
import com.efficient.camera.model.dto.HkwsEventDTO;
import com.efficient.camera.model.entity.CameraHkws;
import com.efficient.camera.properties.CameraProperties;
import com.efficient.common.exception.CommonException;
import com.efficient.common.result.Result;
import com.efficient.common.util.JackSonUtil;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.hikvision.artemis.sdk.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2025/1/13 10:04
 */
@Service
@Slf4j
public class HkwsServiceImpl extends ServiceImpl<CameraHkwsMapper, CameraHkws> implements HkwsService {
    private static final String CONTENT_TYPE = "application/json";
    @Autowired
    private CameraProperties cameraProperties;
    private CameraProperties.HkwsProperties hkwsProperties;
    @PostConstruct
    public void init() {
        this.hkwsProperties = cameraProperties.getHkws();
    }

    public static String getISO8601Timestamp(Date date) {
        TimeZone tz = TimeZone.getTimeZone("Asia/Shanghai");
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        df.setTimeZone(tz);
        String nowAsISO = df.format(date);
        return nowAsISO;
    }



    /**
     * 调用POST请求类型接口，分页获取监控点资源
     * 接口实际url：https://ip:port/artemis/api/resource/v1/cameras
     *
     * @return
     */
    public Result callPostApiGetResources() {
        /**
         * https://ip:port/artemis/api/resource/v1/cameras
         * 过查阅AI Cloud开放平台文档或网关门户的文档可以看到分页获取监控点资源的定义,这是一个POST请求的Rest接口, 入参为JSON字符串，接口协议为https。
         * ArtemisHttpUtil工具类提供了doPostStringArtemis调用POST请求的方法，入参可传JSON字符串, 请阅读开发指南了解方法入参，没有的参数可传null
         */
        String url = "/api/resource/v1/cameras";
        Map<String, Object> paramMap = new HashMap<>();// post请求Form表单参数
        paramMap.put("pageNo", "1");
        paramMap.put("pageSize", "200");
        String result = postHkws(url, paramMap);
        JSONObject entries = JSONUtil.parseObj(result);
        if (!StrUtil.equals(entries.getStr("code"), "0")) {
            return Result.fail(entries.getStr("msg"));
        }
        JSONArray jsonArray = entries.getJSONObject("data").getJSONArray("list");
        List<CameraHkws> hkwsList = new ArrayList<>();
        for (Object object : jsonArray) {
            JSONObject jsb = (JSONObject) object;
            String cameraIndexCode = jsb.getStr("cameraIndexCode");
            String cameraName = jsb.getStr("cameraName");
            String latitude = jsb.getStr("latitude");
            String longitude = jsb.getStr("longitude");
            CameraHkws hkws = new CameraHkws();
            hkws.setCameraIndexCode(cameraIndexCode);
            hkws.setCameraName(cameraName);
            hkws.setContent(JackSonUtil.toJson(object));
            hkws.setIsDelete(0);
            hkwsList.add(hkws);
            log.info("摄像头信息：{}-{}-{}-{}", cameraIndexCode, cameraName, latitude, longitude);
        }
        this.saveOrUpdateBatch(hkwsList);
        return Result.ok(hkwsList);
    }

    /**
     * 调用POST请求类型接口，获取监控点预览取流URL
     * 接口实际url：https://ip:port/artemis/api/video/v2/cameras/previewURLs
     *
     * @return
     */
    @Override
    public String callPostApiPreviewUrl(String cameraIndexCode, String protocol) throws Exception {
        String url = "/api/video/v2/cameras/previewURLs";
        Map<String, Object> paramMap = new HashMap<String, Object>();// post请求Form表单参数
        paramMap.put("cameraIndexCode", cameraIndexCode);
        paramMap.put("streamType", 0);
        paramMap.put("protocol", protocol);
        // paramMap.put("streamform", "rtp");
        String result = postHkws(url, paramMap);
        JSONObject entries = JSONUtil.parseObj(result);
        String str = entries.getJSONObject("data").getStr("url");
        if (StrUtil.isNotBlank(str)) {
            // http://127.0.0.1:83/openUrl/4mkRm7u/live.m3u8
            URL previewUrl = new URL(str);
            return previewUrl.getPath();
        }
        return str;
    }

    @Override
    public Result getEventSubscriptionByEventTypes(HkwsEventDTO hkwsEventDTO) {
        String url = "/api/eventService/v1/eventSubscriptionByEventTypes";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<Integer> eventTypeList = hkwsEventDTO.getEventTypeList();
        Integer[] eventTypes = eventTypeList.toArray(new Integer[0]);
        paramMap.put("eventTypes", eventTypes);
        paramMap.put("eventDest", hkwsEventDTO.getCallBackUrl());
        paramMap.put("subType", 2);
        String result = this.postHkws(url, paramMap);
        return Result.ok(result);
    }

    @Override
    public Result getEventUnSubscriptionByEventTypes(HkwsEventDTO hkwsEventDTO) {
        String url = "/api/eventService/v1/eventUnSubscriptionByEventTypes";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        List<Integer> eventTypeList = hkwsEventDTO.getEventTypeList();
        Integer[] eventTypes = eventTypeList.toArray(new Integer[0]);
        paramMap.put("eventTypes", eventTypes);
        String result = postHkws(url, paramMap);
        return Result.ok(result);
    }

    @Override
    public Result getEventSubscriptionView() {
        String url = "/api/eventService/v1/eventSubscriptionView";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        return Result.ok(postHkws(url, paramMap));
    }

    @Override
    public Result controlling(HkwsControllingDTO dto) {
        String url = "/api/video/v1/ptzs/controlling";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("cameraIndexCode", dto.getCameraIndexCode());
        paramMap.put("action", dto.getAction());
        paramMap.put("command", dto.getCommand());
        return Result.ok(postHkws(url, paramMap));
    }

    @Override
    public Result manualCapture(String cameraIndexCode) throws Exception {
        String url = "/api/video/v1/manualCapture";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("cameraIndexCode", cameraIndexCode);
        String result = postHkws(url, paramMap);
        JSONObject entries = JSONUtil.parseObj(result);
        JSONObject data = entries.getJSONObject("data");
        String picUrl = data.getStr("picUrl");
        if (StrUtil.isNotBlank(picUrl)) {
            // https://127.0.0.1:443/ngx/proxy?i=************************************************************************************************************************************************************************************************************************************************************************************************
            URL urlPath = new URL(picUrl);
            String path = urlPath.getPath();
            String query = urlPath.getQuery();
            // 拼接 path 和 query，前面加上 /
            String finalResult = path + "?" + query;
            return Result.ok(finalResult);
        }
        return Result.ok(picUrl);
    }

    private String postHkws(String url, Map<String, Object> paramMap) {
        String getEventSubscriptionApi = hkwsProperties.getArtemisPath() + url;
        String body = JackSonUtil.toJson(paramMap);
        log.info("请求url: {} ,请求参数：{}", url, body);
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put(hkwsProperties.getProtocol(), getEventSubscriptionApi);
            }
        };
        String result = null;
        try {
            result = ArtemisHttpUtil.doPostStringArtemis(getConfig(), path, body, null, null, CONTENT_TYPE);
            log.info("请求url: {} ,返回结果：{}", url, result);
            JSONObject entries = JSONUtil.parseObj(result);
            if (!StrUtil.equals(entries.getStr("code"), "0")) {
                throw new CommonException(entries.getStr(entries.getStr("msg")));
            }
        } catch (Exception e) {
            throw new CommonException(e.getMessage());
        }
        return null;
    }

    private ArtemisConfig getConfig() {
        Constants.DEFAULT_TIMEOUT = hkwsProperties.getDefaultTimeout();
        Constants.SOCKET_TIMEOUT = hkwsProperties.getSocketTimeout();
        return new ArtemisConfig(hkwsProperties.getHost(), hkwsProperties.getAppKey(), hkwsProperties.getAppSecret());
    }
}
