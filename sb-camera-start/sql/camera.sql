DROP TABLE IF EXISTS "camera_hkws";
CREATE TABLE "camera_hkws" (
                                     "cameraIndexCode" VARCHAR(100) NOT NULL,
                                     "cameraName" VARCHAR(200) ,
                                     "content" text,
                                     "create_user"  varchar(64),
                                     "create_time"  TIMESTAMP,
                                     "update_user"  varchar(64),
                                     "update_time"  TIMESTAMP,
                                     "is_delete"  SMALLINT not null DEFAULT 0,
                                     CONSTRAINT "camera_hkws_pkey" PRIMARY KEY ("cameraIndexCode")
);

COMMENT ON TABLE place_camera_hkws IS '海康威视摄像头信息';

COMMENT ON COLUMN place_camera_hkws.cameraIndexCode IS '摄像头唯一标识';
COMMENT ON COLUMN place_camera_hkws.cameraName IS '摄像头名称';
COMMENT ON COLUMN place_camera_hkws."content" IS '内容';