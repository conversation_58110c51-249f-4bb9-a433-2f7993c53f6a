{"groups": [{"name": "com.efficient.cache", "type": "com.efficient.cache.properties.CacheProperties", "sourceType": "com.efficient.cache.config.CacheConfig"}], "properties": [{"name": "com.efficient.cache.active", "type": "com.efficient.cache.constant.CacheEnum", "description": "缓存插件类型", "defaultValue": "ehcache", "sourceType": "com.efficient.cache.properties.CacheProperties"}, {"name": "com.efficient.cache.ehCache", "type": "com.efficient.cache.properties.EhCache", "description": "ehCache 缓存配置", "defaultValue": "ehcache", "sourceType": "com.efficient.cache.properties.EhCacheProperties"}, {"name": "com.efficient.cache.ehcache.path", "type": "java.lang.String", "description": "文件存储路径", "defaultValue": "/ehcache.xml", "sourceType": "com.efficient.cache.properties.EhCacheProperties"}, {"name": "com.efficient.cache.redis", "type": "com.efficient.cache.properties.Redis", "description": "redis 缓存配置", "defaultValue": "ehcache", "sourceType": "com.efficient.cache.properties.RedisProperties"}, {"name": "com.efficient.cache.redis.enableRedisson", "type": "java.lang.Bo<PERSON>an", "description": "是否 启用 <PERSON><PERSON><PERSON>,默认 false", "defaultValue": false, "sourceType": "com.efficient.cache.properties.RedisProperties"}, {"name": "com.efficient.cache.redis.customRedisson", "type": "java.lang.Bo<PERSON>an", "description": "是否自定义 redisson  url ,默认 false", "defaultValue": "false", "sourceType": "com.efficient.cache.properties.RedisProperties"}, {"name": "com.efficient.cache.redis.redissonUrl", "type": "java.lang.String", "description": "自定义路由", "defaultValue": null, "sourceType": "com.efficient.cache.properties.RedisProperties"}], "hints": [{"name": "com.efficient.cache.active", "values": [{"value": "ehcache", "description": "ehcache"}, {"value": "redis", "description": "redis"}]}]}